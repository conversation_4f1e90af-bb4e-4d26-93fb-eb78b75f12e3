"use client";

import "./player.css";

import {
  MdFormatListNumbered,
  MdOutlineFavorite,
  MdOutlineFavoriteBorder,
  MdOutlineFormatListBulleted,
  MdOutlinePause,
  MdOutlinePlayArrow,
  MdOutlineRepeatOne,
  MdOutlineShare,
  MdOutlineSkipNext,
  MdOutlineSkipPrevious,
  MdShuffle,
  MdClose,
  MdVolumeUp,
} from "react-icons/md";
import React, { useContext, useEffect, useRef, useState } from "react";

import { AiOutlineSound } from "react-icons/ai";
import Image from "next/image";
import Share from "../share";
import { Song } from "@/types/home/<USER>";
import { useAppContext } from "@/contexts/app";
import { useRouter } from "next/navigation";

export default function () {
  const router = useRouter();
  const { user } = useAppContext();
  const {
    playlist,
    currentSong,
    setCurrentSong,
    currentSongIndex,
    setCurrentSongIndex,
    setIsShowSignPanel,
  } = useAppContext();

  const [song, setSong] = useState<Song | null>(null);

  const [isPlaying, setIsPlaying] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [playMode, setPlayMode] = useState("sequence"); // sequence, loop, shuffle
  const [isPlayerVisible, setIsPlayerVisible] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.5);
  const [splitLyrics, setSplitLyrics] = useState("");
  const [currentLine, setCurrentLine] = useState(0);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const lyricsRef = useRef<HTMLDivElement>(null);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    if (isPlaying) {
      audio.pause();
    } else {
      audioPlay(audio);
    }
    setIsPlaying(!isPlaying);
  };

  const fetchFavoriteSong = async function (song_uuid: string) {
    try {
      const params = {
        song_uuid: song_uuid,
      };
      const resp = await fetch("/api/get-favorite-song", {
        method: "POST",
        headers: {
          "Content-Type": "applicaion/json",
        },
        body: JSON.stringify(params),
      });
      const { data } = await resp.json();

      if (data && data.status === "on") {
        setIsLiked(true);
      } else {
        setIsLiked(false);
      }
    } catch (e) {
      console.log("fetch favorite song failed:", e);
    }
  };

  const updateFavoriteSong = async function (song_uuid: string) {
    try {
      const params = {
        song_uuid: song_uuid,
        status: isLiked ? "off" : "on",
      };
      const resp = await fetch("/api/update-favorite-song", {
        method: "POST",
        headers: {
          "Content-Type": "applicaion/json",
        },
        body: JSON.stringify(params),
      });
      const { data } = await resp.json();

      if (data && data.status === "on") {
        setIsLiked(true);
      } else {
        setIsLiked(false);
      }
    } catch (e) {
      console.log("update favorite song failed:", e);
    }
  };

  const updatePlaySong = async function (song_uuid: string) {
    try {
      const params = {
        song_uuid: song_uuid,
      };
      const resp = await fetch("/api/update-play-song", {
        method: "POST",
        headers: {
          "Content-Type": "applicaion/json",
        },
        body: JSON.stringify(params),
      });
      const { code, message } = await resp.json();

      if (code !== 0) {
        console.log("update play song failed: ", message);
      }
    } catch (e) {
      console.log("update play song failed:", e);
    }
  };

  const toggleLike = () => {
    if (!song || !song.uuid) {
      return;
    }
    if (!user || !user.uuid) {
      setIsShowSignPanel(true);
      return;
    }

    updateFavoriteSong(song.uuid);
  };

  const togglePlayMode = () => {
    if (playMode === "sequence") {
      setPlayMode("shuffle");
    } else if (playMode === "shuffle") {
      setPlayMode("loop");
    } else {
      setPlayMode("sequence");
    }
  };

  const changeProgress = (event: { clientX: any }) => {
    const audio = audioRef.current;
    const progressBar = progressBarRef.current;
    if (!audio || !progressBar) {
      return;
    }

    const clickX = event.clientX;
    const { left, width } = progressBar.getBoundingClientRect();
    const clickProgress = (clickX - left) / width;
    const newTime = clickProgress * audio.duration;
    audio.currentTime = newTime;

    setProgress(clickProgress);
  };

  const startDrag = (event: { clientX: any }) => {
    setIsDragging(true);
    changeProgress(event);
  };

  const onDragging = (event: { clientX: any }) => {
    if (isDragging) {
      changeProgress(event);
    }
  };

  const stopDrag = () => {
    setIsDragging(false);
  };

  const playNext = () => {
    if (!playlist) {
      return;
    }
    switch (playMode) {
      case "sequence":
        setCurrentSongIndex(
          (prevIndex: number) => (prevIndex + 1) % playlist.length
        );
        break;
      case "loop":
        if (audioRef.current) {
          audioPlay(audioRef.current);
        }
        break;
      case "shuffle":
        let nextIndex,
          currentIndex = currentSongIndex;
        do {
          nextIndex = Math.floor(Math.random() * playlist.length);
        } while (playlist.length > 1 && nextIndex === currentIndex);
        setCurrentSongIndex(nextIndex);
        break;
      default:
        console.log(`Unsupported play mode: ${playMode}`);
    }
  };

  const playPrev = () => {
    if (!playlist) {
      return;
    }
    switch (playMode) {
      case "sequence":
        setCurrentSongIndex(
          (prevIndex: number) =>
            (prevIndex - 1 + playlist.length) % playlist.length
        );
        break;
      case "loop":
        if (audioRef.current) {
          audioPlay(audioRef.current);
        }
        break;
      case "shuffle":
        let nextIndex,
          currentIndex = currentSongIndex;
        do {
          nextIndex = Math.floor(Math.random() * playlist.length);
        } while (playlist.length > 1 && nextIndex === currentIndex);
        setCurrentSongIndex(nextIndex);
        break;
      default:
        console.log(`Unsupported play mode: ${playMode}`);
    }
  };

  const audioPlay = (audio: HTMLAudioElement) => {
    audio.play().catch((error) => {
      if (error.name === "AbortError") {
        console.log("Play() was interrupted");
      } else {
        console.error("Error occurred while playing the video:", error);
      }
    });
  };

  useEffect(() => {
    document.addEventListener("mousemove", onDragging);
    document.addEventListener("mouseup", stopDrag);

    return () => {
      document.removeEventListener("mousemove", onDragging);
      document.removeEventListener("mouseup", stopDrag);
    };
  }, [isDragging]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const updateProgress = () => {
      if (!isDragging) {
        const progress = audio.currentTime / audio.duration;
        setCurrentTime(audio.currentTime);
        setProgress(isNaN(progress) ? 0 : progress);
      }
    };

    const setAudioDuration = () => setDuration(audio.duration || 0);

    audio.addEventListener("timeupdate", updateProgress);
    audio.addEventListener("durationchange", setAudioDuration);

    return () => {
      audio.removeEventListener("timeupdate", updateProgress);
      audio.removeEventListener("durationchange", setAudioDuration);
    };
  }, [isDragging]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const updateCurrentLine = () => {
      if (!audio.duration) return;
      const lineDuration = audio.duration / splitLyrics.length;
      const currentLineIndex = Math.floor(audio.currentTime / lineDuration);
      setCurrentLine(currentLineIndex);
    };

    const intervalId = setInterval(updateCurrentLine, 500);

    return () => clearInterval(intervalId);
  }, [splitLyrics.length]);

  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = volume;
    }
  }, [volume]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleSongEnd = () => {
      playNext();
    };

    audio.addEventListener("ended", handleSongEnd);

    return () => {
      audio.removeEventListener("ended", handleSongEnd);
    };
  }, [currentSongIndex, playMode]);

  useEffect(() => {
    if (
      !playlist ||
      playlist.length === 0 ||
      playlist.length - 1 < currentSongIndex
    ) {
      return;
    }

    setCurrentSong(playlist[currentSongIndex]);
  }, [currentSongIndex]);

  useEffect(() => {
    if (currentSong) {
      const lyrics = currentSong.lyrics ? currentSong.lyrics.split("\n") : [];
      setSplitLyrics(lyrics);
      setSong(currentSong);
      fetchFavoriteSong(currentSong.uuid);
    }
  }, [currentSong]);

  useEffect(() => {
    if (!song || !song.audio_url) {
      return;
    }

    const audio = audioRef.current;
    if (!audio) {
      return;
    }

    const updateProgress = () => {
      if (!isDragging) {
        const progress = audio.currentTime / audio.duration;
        setCurrentTime(audio.currentTime);
        setProgress(isNaN(progress) ? 0 : progress);
      }
    };

    const setAudioDuration = () => setDuration(audio.duration || 0);

    audio.addEventListener("timeupdate", updateProgress);
    audio.addEventListener("durationchange", setAudioDuration);

    setIsPlaying(true);
    audio.src = song.audio_url;
    audioPlay(audio);

    updatePlaySong(song.uuid);

    return () => {
      audio.removeEventListener("timeupdate", updateProgress);
      audio.removeEventListener("durationchange", setAudioDuration);
    };
  }, [song]);

  useEffect(() => {
    const audio = audioRef.current;

    if (!audio) {
      return;
    }

    // audio.play();
  }, [audioRef.current]);

  useEffect(() => {}, []);

  return (
    <>
      {/* Always show player when visible, show placeholder when no song */}
      <div
        className={`fixed bottom-0 left-0 z-[1000] w-full transition-all duration-200 ease-in-out ${
          isPlayerVisible
            ? "transform-none opacity-100"
            : "transform translate-y-full opacity-0"
        }`}
      >
        {song && <audio src={song.audio_url} ref={audioRef} />}

        {/* Black background overlay */}
        <div className="fixed bottom-0 h-[72px] w-full bg-black transition-all duration-200 ease-in-out" />

        {/* Main player container */}
        <div className="relative border-t border-muted bg-black px-4 py-2">
          <div className="flex h-[72px] items-center justify-between">
            {/* Left section - Song info */}
            <div className="flex h-full w-1/4 items-center justify-between">
              <div className="flex w-full gap-4 text-white">
                <div className="relative size-10 shrink-0">
                  {song?.image_url ? (
                    <Image
                      src={song.image_url}
                      width={40}
                      height={40}
                      alt={song.title || ""}
                      className="rounded object-cover w-full h-full cursor-pointer"
                      onClick={() => router.push(`/song/${song.uuid}`)}
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-600 rounded flex items-center justify-center">
                      <MdOutlinePlayArrow className="text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="hidden h-full grow flex-col justify-around text-ellipsis sm:flex">
                  <div
                    className="truncate mb-0 w-full text-[14px] hover:underline cursor-pointer"
                    onClick={() => song && router.push(`/song/${song.uuid}`)}
                  >
                    {song?.title || "No song selected"}
                  </div>
                  <div className="truncate text-[14px] text-gray-400">
                    {song ? (
                      <>
                        {formatTime(currentTime)} / {formatTime(duration)}
                        <span
                          className={`ml-2 px-1 py-0.5 text-xs rounded ${
                            song.provider === "udio"
                              ? "bg-red-500 text-white"
                              : "bg-red-600 text-white"
                          }`}
                        >
                          {song.provider}
                        </span>
                      </>
                    ) : (
                      "Select a song to play"
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Center section - Controls and progress */}
            <div className="w-1/2">
              <div className="flex w-full flex-col items-center justify-center text-white">
                {/* Control buttons */}
                <div className="flex items-center gap-6 px-2">
                  <button
                    onClick={togglePlayMode}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white"
                    title="Play Mode"
                    disabled={!song}
                  >
                    {playMode === "sequence" ? (
                      <MdFormatListNumbered className="text-lg" />
                    ) : playMode === "shuffle" ? (
                      <MdShuffle className="text-lg" />
                    ) : (
                      <MdOutlineRepeatOne className="text-lg text-red-500" />
                    )}
                  </button>

                  <button
                    onClick={playPrev}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Previous"
                    disabled={!song}
                  >
                    <MdOutlineSkipPrevious className="text-xl text-white" />
                  </button>

                  <button
                    onClick={togglePlay}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Play/Pause"
                    disabled={!song}
                  >
                    {isPlaying ? (
                      <MdOutlinePause className="text-2xl text-white" />
                    ) : (
                      <MdOutlinePlayArrow className="text-2xl text-white" />
                    )}
                  </button>

                  <button
                    onClick={playNext}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Next"
                    disabled={!song}
                  >
                    <MdOutlineSkipNext className="text-xl text-white" />
                  </button>

                  <button
                    onClick={toggleLike}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Like"
                    disabled={!song}
                  >
                    {isLiked ? (
                      <MdOutlineFavorite className="text-lg text-red-500" />
                    ) : (
                      <MdOutlineFavoriteBorder className="text-lg" />
                    )}
                  </button>
                </div>

                {/* Progress bar */}
                <div className="flex h-5 w-full items-center justify-center gap-2 px-4 text-sm font-medium text-gray-400 mt-2">
                  <div className="flex items-center text-xs">
                    {formatTime(currentTime)}
                  </div>
                  <div className="flex w-full items-center">
                    <div
                      className="w-full relative cursor-pointer"
                      ref={progressBarRef}
                      onClick={song ? changeProgress : undefined}
                      onMouseDown={song ? startDrag : undefined}
                    >
                      <div className="w-full h-1 bg-gray-600 rounded">
                        <div
                          className="h-1 bg-red-500 rounded relative"
                          style={{ width: `${progress * 100}%` }}
                        >
                          <div
                            className="absolute -top-1 w-3 h-3 bg-red-500 rounded-full"
                            style={{ right: "-6px" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center text-xs">
                    {formatTime(duration)}
                  </div>
                </div>
              </div>
            </div>

            {/* Right section - Volume and close */}
            <div className="w-1/4">
              <div className="flex flex-row items-center justify-end gap-2">
                <div className="hidden shrink-0 gap-2 lg:flex">
                  {song && <Share song={song} />}
                </div>

                {/* Volume control */}
                <div className="group/volume flex h-10 w-10 flex-col justify-end">
                  <div className="relative flex h-10 w-10 shrink-0 flex-col items-end justify-end gap-2 overflow-hidden rounded-full border border-transparent text-gray-400 transition-all delay-150 duration-300 hover:text-white group-hover/volume:h-[120px] group-hover/volume:border-gray-600 group-hover/volume:bg-gray-800 group-hover/volume:delay-0">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className="absolute -left-[31px] top-8 z-50 hidden w-[100px] -rotate-90 opacity-0 transition-opacity delay-0 duration-300 group-hover/volume:opacity-100 group-hover/volume:delay-150 sm:block appearance-none bg-gray-600 h-1 rounded"
                      style={{
                        background: `linear-gradient(90deg, #dc2626 0% ${volume * 100}%, #4b5563 ${volume * 100}%)`,
                      }}
                    />
                    <button
                      className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 shrink-0 !border-none text-gray-400 hover:!bg-transparent hover:text-white"
                      onClick={() => setVolume(volume > 0 ? 0 : 0.5)}
                      title="Volume"
                    >
                      <MdVolumeUp className="text-lg" />
                    </button>
                  </div>
                </div>

                {/* Close button */}
                <button
                  onClick={() => setIsPlayerVisible(false)}
                  className="flex h-10 w-10 items-center justify-center rounded-full border border-transparent text-gray-400 transition-colors duration-200 hover:border-gray-600 hover:bg-gray-800 hover:text-white"
                  title="Hide Player"
                >
                  <MdClose className="text-lg" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Show player button when hidden */}
      {!isPlayerVisible && (
        <button
          onClick={() => setIsPlayerVisible(true)}
          className="fixed bottom-4 right-4 z-[1001] bg-red-600 hover:bg-red-700 text-white p-3 rounded-full shadow-lg transition-all duration-200"
          title="Show Player"
        >
          <MdOutlinePlayArrow className="text-xl" />
        </button>
      )}
    </>
  );
}
