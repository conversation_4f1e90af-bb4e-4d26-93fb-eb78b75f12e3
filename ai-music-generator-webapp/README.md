# AI Music Generator Server

A Next.js-based web application that provides AI music generation capabilities through Suno.ai integration, featuring user management, payment processing, and comprehensive music creation APIs.

## 🏗️ Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── api/                     # API Routes
│   │   ├── suno/               # Suno.ai integration endpoints
│   │   │   ├── generate/       # Music generation
│   │   │   ├── generateLyrics/ # Lyrics generation
│   │   │   ├── batchQueryV4/   # Batch status queries
│   │   │   └── myList/         # User music library
│   │   ├── aigc/               # AI content management
│   │   │   ├── user/           # User operations
│   │   │   ├── pay/            # Payment processing
│   │   │   └── config/         # Configuration management
│   │   ├── process/            # Background processing
│   │   └── notice/             # Notification system
│   ├── components/             # React components
│   └── docs/                   # API documentation
├── lib/                        # Core libraries
│   ├── SunoApi.ts             # Suno.ai API integration
│   ├── http.ts                # HTTP utilities
│   ├── qiniu.ts               # Cloud storage (Qiniu)
│   └── wxpay.ts               # WeChat Pay integration
├── models/                     # Database models (Sequelize)
├── services/                   # Business logic layer
├── types/                      # TypeScript type definitions
└── data/                      # Database schemas
```

## ✨ Core Features

### Music Generation

- **AI Music Creation**: Generate original music using Suno.ai's advanced AI models
- **Inspiration Mode**: Create music from simple text prompts
- **Custom Mode**: Full control over lyrics, style, and title
- **Lyrics Generation**: AI-powered lyric creation from prompts
- **Batch Processing**: Handle multiple music generation requests efficiently

### User Management

- **Multi-platform Authentication**: Support for WeChat Mini Program and web login
- **Credit System**: Token-based usage tracking and management
- **User Profiles**: Complete user information and preferences management
- **Subscription Packages**: Flexible pricing tiers and payment options

### Payment Integration

- **WeChat Pay**: Native WeChat payment integration
- **Package Management**: Various subscription and one-time purchase options
- **Order Processing**: Complete order lifecycle management
- **Credit Top-up**: Seamless credit purchase and allocation

### Content Management

- **Music Library**: Personal music collection with metadata
- **Cloud Storage**: OSS integration for media file management
- **Status Tracking**: Real-time generation status monitoring
- **Public Sharing**: Option to make creations publicly accessible

### API & Integration

- **RESTful APIs**: Comprehensive API endpoints for all features
- **OpenAI Compatible**: `/v1/chat/completions` endpoint for GPT integration
- **Swagger Documentation**: Interactive API documentation
- **Webhook Support**: Real-time notifications and callbacks

## 🛠️ Key Technologies

### Backend Framework

- **Next.js 14**: Full-stack React framework with App Router
- **TypeScript**: Type-safe development environment
- **Express.js**: Additional server functionality

### Database & Storage

- **PostgreSQL**: Primary database with Sequelize ORM
- **Redis (ioredis)**: Caching and session management
- **Qiniu Cloud**: Object storage for media files

### AI & External APIs

- **Suno.ai Integration**: Core music generation capabilities
- **Custom API Wrapper**: Robust Suno.ai API client with session management

### Payment & Authentication

- **WeChat Pay**: Integrated payment processing
- **JWT Authentication**: Secure user authentication
- **Multi-platform Support**: Web and WeChat Mini Program

### Development & Deployment

- **Docker**: Containerized deployment
- **Vercel**: Cloud deployment support
- **pnpm**: Package management
- **ESLint**: Code quality assurance
- **Tailwind CSS**: Utility-first styling

### Monitoring & Logging

- **Pino**: High-performance logging
- **Winston**: Advanced logging with rotation
- **Vercel Analytics**: Usage analytics

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Redis server
- Suno.ai account and cookies

### Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/ai_music
REDIS_URL=redis://localhost:6379

# Suno.ai
SUNO_COOKIE=your_suno_cookie_here

# WeChat Pay (optional)
WXPAY_MCHID=your_merchant_id
WXPAY_PRIVATE_KEY=your_private_key
WXPAY_CERTIFICATE=your_certificate

# Cloud Storage (optional)
QINIU_ACCESS_KEY=your_access_key
QINIU_SECRET_KEY=your_secret_key
QINIU_BUCKET=your_bucket_name

# Application
NEXTAUTH_SECRET=your_nextauth_secret
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Installation & Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ai-music-generator-webapp
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Database setup**

   ```bash
   # Create database and run migrations
   psql -U postgres -d ai_music -f src/data/install.sql
   ```

4. **Development server**

   ```bash
   pnpm dev
   ```

5. **Access the application**
   - Web interface: http://localhost:3000
   - API documentation: http://localhost:3000/docs

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t ai-music-generator .
docker run -p 3000:3000 --env-file .env ai-music-generator
```

### Production Deployment

For production deployment on Vercel:

1. Connect your repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 📚 API Usage

### Generate Music

```bash
POST /api/suno/generate
Content-Type: application/json
x-user-id: user-uuid

{
  "inputType": 10,
  "idea": "A cheerful pop song about summer",
  "makeInstrumental": false,
  "wait_audio": true
}
```

### Generate Lyrics

```bash
POST /api/suno/generateLyrics
Content-Type: application/json

{
  "idea": "A song about overcoming challenges"
}
```

### Check User Credits

```bash
POST /api/aigc/user/getRemainCount
Content-Type: application/json
x-user-id: user-uuid
```

For complete API documentation, visit `/docs` when running the application.

## 📄 License

This project is licensed under the LGPL-3.0-or-later License. See the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support and questions, please check the [FAQ](FAQ.md) or open an issue on GitHub.
