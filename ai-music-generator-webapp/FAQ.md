# FAQ

## IMPORTANT！the process of generate music and batchQuery audioInfo

1. 生成歌曲
   generate(idea) --> suno.generate(idea)?optimize --> relate music_ids with task_id --> return{task_id}
2. 批量查询并更新数据
   batchQuery(taks_ids) --> findTasks(taks_ids) --> suno.get(task.music_ids) ...call multiple times get cdn_url when complete... --> ? downloadFile(cdn_url) --> uploadToQiniu(file){oss_url} --> updateMusicOss(oss_url) --> updateMusicInfo(music) ? --> updateTask(task_id, audioInfos) --> return{status, list[{audioInfo{cdn_url}}]}
   说明：因为小程序只能访问备案域名，suno 返回的 cdn 无法访问，需下载转存 oss 获得可访问链接。
   实现：轮询调度`batchQuery`，`suno.get()`返回`audioInfo`。

   - `updateTask(audioInfo)` 更新任务数据；
   - `updateMusicInfo(music)` 更新音乐状态、标题等数据；
   - if `complete`， Promise 并发执行 `downloadFile` from `audioInfo.cdn_url` and `uploadToQiniu` to get `oss_url` update `music.oss_url`。

   注意：`suno.get()`,`downloadFile`,`uploadToQiniu`容易超时，特别是 mp4 文件，需要做错误处理。
   优化：目前实现需要等待 `suno.generate` 直到 `suno.get()` 返回的 `audioInfo.status`="complete"，一次长时间等待；之后还要 `downloadFile` and `uploadToQiniu`，两次长时间等待；总共三次长时间等待。

   - 当 `audioInfo.status`="complete"，还会轮询调用`batchQuery`，但只调用一次 `downloadFile` and `uploadToQiniu` 直到成功获取 `oss_url`。
   - `suno.get()`必须执行等待返回`audioInfo`，suno 官方优化是 streaming 立即返回 pipeline 链接[https://audiopipe.suno.ai/?item_id=c7bbd0b5-2fd4-47b5-a219-d057d6cd89d2]；
   - `downloadFile`,`uploadToQiniu` 是否可以在别的地方执行？不影响`batchQuery`。

3. 获取余额
   getRemainCount() --> return{leftCoin}
4. 获取我的歌曲列表
   myList() --> return{records[{music{oss_url}}]}

### Q1: download and upload may failed and mp4 is long time when batchQuery

#### A1: only retry download and upload mp3 when complete

## Update oss_url of music assets

1. `getMusicIdsExistAllOss() return exist_music_ids`: 获取不需要更新 `oss` 的 `music_ids`(`oss_video_url`, `oss_audio_url`, `oss_image_url` 都不为空)
2. `getMusicsFromFile() return musics`: 从文件获取音乐数据（包括`music_id`, `video_url`, `audio_url`, `image_url`等数据）
3. 判断：
   3.1. 如果`exist_music_ids`已经包含`musics.music_id`，则跳过；
   3.2. 否则，进行以下操作：
   3.2.1. `processResoures(music)`: 处理资源，尝试将 `music.video_url`, `music.audio_url`, `music.image_url` 的资源文件下载到缓存区 `downloadFile(music)`，并上传到七牛云`uploadToQiniu(music)`，获得七牛云返回的 `oss_video_url`, `oss_audio_url`, `oss_image_url` 赋值给 `music`。
   3.2.2. `updateOssUrls(music)`: 更新数据表，`music.video_url`, `music.audio_url`, `music.image_url` 如果某个值不为空，更新`music`表对应的字段 `oss_video_url`, `oss_audio_url`, `oss_image_url`。

### Q1: 效率问题

#### ANSWER

1. `checkUrlIsProcessed`: 标记 URL，避免重复下载和上传。
2. `Promise.allSettled`: 并行处理下载和上传任务。`music.video_url`, `music.audio_url`, `music.image_url` 三个资源的任务相互独立。
3. `retryOperation`: 超时处理和失败重试。下载和上传文件都有可能失败，尤其是 `music.video_url` 文件较大，等待时间要设置稍大。如果失败进行重试，重试时可以使用指数退避策略。

### Q2: 调用问题：栈溢出 `Maximum call stack size exceeded`

#### ANSWER

设置最大并发数，间隔 5 秒调用一次。

## TypeError: hexoid is not a function

### A: `next.config.mjs`

```Javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
   webpack: (config, { isServer }) => {
     if (isServer) {
       return {
         ...config,
         resolve: {
           ...config.resolve,
           alias: { ...config.resolve.alias, hexoid: "hexoid/dist/index.js" },
         },
         // 引入微信支付相关包后，警告【Critical dependency: the request of a dependency is an expression】关闭
         module: { ...config.module, exprContextCritical: false },
       };
     }
     return config;
   },
 };

 export default nextConfig;
```

- **条件判断**: `if (isServer) { ... }` 这部分代码确保只修改服务器端的 Webpack 配置。这是因为 Next.js 的一些配置需要区分服务端和客户端，以适应不同的运行环境。
- **配置继承**: `...config` 表示继承已有的 Webpack 配置，这是修改配置时的常见做法，以确保不会意外覆盖其他重要的配置。

- **别名设置 (`alias`)**:

  - `alias: { ...config.resolve.alias, hexoid: "hexoid/dist/index.js" }`
  - 这里设置了 `hexoid` 模块的别名，指向 `"hexoid/dist/index.js"`。这样的设置通常用来确保正确的文件被引用，特别是在模块导出时可能存在的路径或文件指定错误。
  - 通过这种方式，代码中所有 `require('hexoid')` 或 `import from 'hexoid'` 的调用都会被重定向到 `hexoid/dist/index.js`，这个文件应该是正确导出函数的版本。

- **关闭警告 (`exprContextCritical`)**:
  - `exprContextCritical: false`
  - 这个配置是用来关闭 Webpack 的一个具体警告：`Critical dependency: the request of a dependency is an expression`。
  - 此警告通常在代码中动态引用模块时触发，例如使用表达式作为 `require` 调用的参数。在某些情况下，这种动态依赖是合法的，但 Webpack 默认会警告开发者这可能是一个潜在的代码问题。
  - 在引入微信支付等复杂库时，可能会遇到这种情况，所以这里选择关闭该警告，以清理控制台输出，让开发者专注于其他可能的错误。

**总结**

这段代码的目的是通过精细的 Webpack 配置调整，解决一些在特定库和工具使用中遇到的问题。配置别名是一个常用的技巧，用于确保正确版本的库被加载，同时关闭一些不必要的警告可以帮助开发者更好地聚焦于重要的问题。这种配置需要对 Webpack 有一定了解，才能正确设置并确保不影响项目的其他部分。

## Cannot find module

### A: remove and install or restart VSCode

```Shell
# 删除文件
rm -rf node_modules
rm pnpm-lock.yaml
# 删除缓存
pnpm store prune
# 重新安装依赖
pnpm install
```

restart VSCode
