import logger from "@/lib/logger";
import { respData, respErr, respJson } from "@/lib/resp";
import wxpay from "@/lib/wxpay";
import { updateOrderStatus } from "@/models/order";
import { PayStatus } from "@/types/common";

export async function POST(req: Request, res: Response) {
  try {
    const body = await req.json();
    logger.info(`---orderNotify req=`, body);

    const headers = req.headers; // 请求头信息
    // 验证必要的头部信息，并设置合理的错误处理
    const signature = headers.get("wechatpay-signature");
    const serial = headers.get("wechatpay-serial");
    const wechatpay_nonce = headers.get("wechatpay-nonce");
    const timestamp = headers.get("wechatpay-timestamp");

    // 检查是否所有必要的头信息都存在
    if (!signature || !serial || !wechatpay_nonce || !timestamp) {
      throw new Error("Missing or invalid required headers.");
    }
    if (!body) {
      throw new Error("Missing or invalid required body.");
    }

    const params = {
      body: JSON.stringify(body),
      signature: signature,
      serial: serial,
      nonce: wechatpay_nonce,
      timestamp: timestamp,
    };

    // logger.debug("verifySign params=", params);

    const ret = await wxpay.verifySign(params); // 执行验签操作
    // logger.debug("验签结果(bool类型)：" + ret);
    if (!ret) {
      throw new Error("Signature verification failed.");
    }

    // Decrypt the resource using the decipher_gcm method
    if (!body.resource || !body.resource.ciphertext) {
      throw new Error("Invalid or missing encrypted resource data.");
    }
    const { ciphertext, associated_data, nonce } = body.resource;
    const decryptedData = wxpay.decipher_gcm(
      ciphertext,
      associated_data,
      nonce
    );

    const parsedData = parseDecryptedData(decryptedData); // Safely parse the decrypted data
    // logger.debug("Decrypted data: ", parsedData);

    // Now you can safely access the data fields
    const { out_trade_no, success_time, transaction_id, trade_state } =
      parsedData;
    // logger.debug(out_trade_no, success_time, transaction_id, trade_state);
    const pay_status =
      trade_state === "SUCCESS" ? PayStatus.Success : PayStatus.Fail; // Assuming 'SUCCESS' maps to 1, other states to 2

    // update pay_status(must), paied_at, nonce of order by out_trade_no
    await updateOrderStatus(out_trade_no, pay_status, success_time);
    // 返回应答 {code=200, message="SUCCESS"}
    return respJson(200, "SUCCESS");
  } catch (error) {
    logger.error("orderNotify error=", error);
    return respErr("orderNotify error"); // 返回错误处理信息
  }
}

interface DecryptedData {
  amount: {
    currency: string;
    payer_currency: string;
    payer_total: number;
    total: number;
  };
  appid: string;
  attach: string;
  bank_type: string;
  mchid: string;
  out_trade_no: string;
  payer: {
    openid: string;
  };
  success_time: string;
  timestamp: string;
  trade_state: string;
  trade_state_desc: string;
  trade_type: string;
  transaction_id: string;
}

function parseDecryptedData(data: unknown): DecryptedData {
  if (typeof data === "object" && data !== null && "out_trade_no" in data) {
    const safeData = data as DecryptedData; // Type assertion based on presence of key
    // Further validation could be added here to check data integrity
    return safeData;
  } else {
    throw new Error("Invalid data format for decryption.");
  }
}
