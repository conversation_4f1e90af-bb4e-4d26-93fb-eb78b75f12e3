import { suno<PERSON><PERSON> } from "@/lib/SunoApi";
import { respData, respErr } from "@/lib/resp";
import { updateMusicInfoV3, updateMusicStatus } from "@/models/music";
import { findTaskByTaskIds, updateTaskWithAudioInfos } from "@/models/task";
import { processOssUrl } from "@/services/music";
import { Music } from "@/types/music";
import { Task } from "@/types/task";
import logger from "@/lib/logger";
import { checkUrlIsProcessed } from "@/models/url_log";

/**
 *  批量查询并更新数据
 *
 * @param ids 任务ID列表
 * @param from 来源。mini=微信小程序
 * @returns 任务列表数据
 */
export async function POST(req: Request) {
  try {
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }
    const body = await req.json();
    // logger.info(`---batchQueryV4 req=`, body);
    const { ids, from } = body;
    if (!ids) {
      return respErr("Invalid params");
    }

    // 根据进行任务ids查询任务信息
    const tasks: Task[] = await findTaskByTaskIds(ids);

    // logger.debug(`---batchQueryV4 tasks=${JSON.stringify(tasks)}`);
    // 初始化最终响应数据的容器
    let responseData: any[] = [];

    for (const task of tasks) {
      const musicIds = task.music_ids;
      if (musicIds && musicIds.length > 0) {
        // call suno api to get audioInfo(maybe long time)
        const getAudioInfos = await (await sunoApi).get(musicIds);
        // logger.debug(
        //   `---batchQueryV4 getAudioInfos=${JSON.stringify(getAudioInfos)}`
        // );
        // update tasks and musics
        // let music_ids: string[] = [];
        for (const audioInfo of getAudioInfos) {
          if (audioInfo) {
            // music_ids.push(audioInfo.id);
            let music: Music = {
              music_id: audioInfo.id,
              user_id: user_id,
              video_url: audioInfo.video_url,
              audio_url: audioInfo.audio_url,
              image_url: audioInfo.image_url,
              model_name: audioInfo.model_name,
              status: audioInfo.status,
              title: audioInfo.title,
              created_at: audioInfo.created_at,
              music_metadata: {
                music_id: audioInfo.id,
                user_id: user_id,
                tags: audioInfo.tags,
                prompt: audioInfo.lyric,
                gpt_description_prompt: audioInfo.gpt_description_prompt,
                type: audioInfo.type,
                duration: Number(audioInfo.duration),
                created_at: audioInfo.created_at,
              },
            };
            if (music.status == "error") {
              await updateMusicStatus(audioInfo.id, "error"); // 使用 async/await 确保异步操作完成
            }
            // 并行处理视频和音频
            const mediaTasks = [];
            if (
              music.video_url &&
              !(await checkUrlIsProcessed(music.video_url))
            ) {
              mediaTasks.push(processOssUrl(music.video_url, "video", music));
            }
            if (
              music.audio_url &&
              !(await checkUrlIsProcessed(music.audio_url))
            ) {
              mediaTasks.push(processOssUrl(music.audio_url, "audio", music));
            }
            await Promise.allSettled(mediaTasks);

            if (music.status == "complete") {
              // logger.debug(`---batchQueryV4 music=${JSON.stringify(music)}`);
              await updateMusicInfoV3(music);
            }
          }
        }

        await updateTaskWithAudioInfos(task.task_id, getAudioInfos);

        // 根据 getAudioInfos.status 设置 task.status
        // task.status 默认为 10
        // 如果 getAudioInfos.status 有一个为 "streaming",设置task.status=20
        // 如果 getAudioInfos.status 都为 "complete",设置task.status=30
        // 根据 getAudioInfos.status 设置 task.status
        let taskStatus: number;
        let errorExists = getAudioInfos.some((info) => info.status === "error");
        let streamingExists = getAudioInfos.some(
          (info) => info.status === "streaming"
        );
        let allComplete = getAudioInfos.every(
          (info) => info.status === "complete"
        );
        if (errorExists) {
          taskStatus = 40; // 如果有错误的音频，设置状态为40
        } else if (streamingExists) {
          taskStatus = 20; // 如果有正在流的音频，设置状态为20
        } else if (allComplete) {
          taskStatus = 30; // 如果所有音频都已完成，设置状态为30
        } else {
          taskStatus = 10; // 默认状态
        }

        // 构造响应数据
        let taskResponse = {
          id: task.task_id,
          status: taskStatus,
          errMsg: null,
          list: getAudioInfos.map((info: any) => ({
            clipId: info.id,
            lyric: info.lyric,
            title: info.title,
            mvVersion: info.mvVersion,
            audioUrl: info.audio_url,
            videoUrl: info.video_url,
            image_url: info.image_url,
            resultMark: 10,
            resultMarkMsg: null,
            state: info.status,
            // ktodo 优化设置 progress
            // progress: calculateProgress(info), // 设置进度
            continueFlag: false, // 为完成时为 false
            music_metadata: {
              tags: info.tags,
              duration: info.duration,
            },
          })),
        };

        // 添加到响应数据容器
        responseData.push(taskResponse);
      }
    }
    // logger.info(`---batchQueryV4 rsp=${JSON.stringify(responseData)}`);

    return respData(responseData);
  } catch (e) {
    console.error("batchQueryV4 error=", e);
    return respErr("batchQueryV4 error=" + e);
  }
}

// 计算任务进度的函数，根据具体业务逻辑实现
function calculateProgress(audioInfo: any): number {
  // 根据具体业务逻辑计算任务进度
  // 示例：假设进度是根据已处理的音频数量来计算
  return (audioInfo.processedCount / audioInfo.totalCount) * 100;
}
