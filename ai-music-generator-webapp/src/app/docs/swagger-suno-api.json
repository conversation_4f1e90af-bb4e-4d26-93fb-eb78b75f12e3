{"openapi": "3.0.3", "info": {"title": "suno-api", "description": "Use API to call the music generation service of Suno.ai and easily integrate it into agents like GPTs.", "version": "1.1.0"}, "tags": [{"name": "default"}], "paths": {"/api/generate": {"post": {"summary": "Generate audio based on Prompt.", "description": "It will automatically fill in the lyrics.\n\n2 audio files will be generated for each request, consuming a total of 10 credits.\n\n`wait_audio` can be set to API mode:\n\n• By default, it is set to `false`, which indicates the background mode. It will only return audio task information, and you will need to call the get API to retrieve detailed audio information.\n\n• If set to `true`, it simulates synchronous mode. The API will wait for a maximum of 100s until the audio is generated, and will directly return the audio link and other information. Recommend using in GPTs and other agents.", "tags": ["default"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["prompt", "make_instrumental", "wait_audio"], "properties": {"prompt": {"type": "string", "description": "Prompt", "example": "A popular heavy metal song about war, sung by a deep-voiced male singer, slowly and melodiously. The lyrics depict the sorrow of people after the war."}, "make_instrumental": {"type": "boolean", "description": "Whether to generate instrumental music", "example": "false"}, "wait_audio": {"type": "boolean", "description": "Whether to wait for music generation, default is false, directly return audio task information; set to true, will wait for up to 100s until the audio is generated.", "example": "false"}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["0", "1"], "properties": [{"$ref": "#/components/schemas/audio_info"}, {"$ref": "#/components/schemas/audio_info"}]}}}}}}}}, "/v1/chat/completions": {"post": {"summary": "Generate audio based on Prompt - OpenAI API format compatibility.", "description": "Convert the `/api/generate` API to be compatible with the OpenAI `/v1/chat/completions` API format. \n\nGenerally used in OpenAI compatible clients.", "tags": ["default"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["prompt", "make_instrumental", "wait_audio"], "properties": {"prompt": {"type": "string", "description": "Prompt", "example": "A popular heavy metal song about war, sung by a deep-voiced male singer, slowly and melodiously. The lyrics depict the sorrow of people after the war."}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "description": "Text description for music, with details like title, album cover, lyrics, and more."}}}}}}}}}, "/api/custom_generate": {"post": {"summary": "Generate Audio - Custom Mode", "description": "The custom mode enables users to provide additional details about the music, such as music genre, lyrics, and more.\n\n 2 audio files will be generated for each request, consuming a total of 10 credits. \n\n `wait_audio` can be set to API mode:\n\n• By default, it is set to false, which indicates the background mode. It will only return audio task information, and you will need to call the get API to retrieve detailed audio information.\n\n• If set to true, it simulates synchronous mode. The API will wait for a maximum of 100s until the audio is generated, and will directly return the audio link and other information. Recommend using in GPTs and other agents.", "tags": ["default"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["prompt", "tags", "title"], "properties": {"prompt": {"type": "string", "description": "Detailed prompt, including information such as music lyrics.", "example": "[Verse 1]\nCruel flames of war engulf this land\nBattlefields filled with death and dread\nInnocent souls in darkness, they rest\nMy heart trembles in this silent test\n\n[Verse 2]\nPeople weep for loved ones lost\nBattered bodies bear the cost\nSeeking peace and hope once known\nOur grief transforms to hearts of stone\n\n[Chorus]\nSilent battlegrounds, no birds' song\nShadows of war, where we don't belong\nMay flowers of peace bloom in this place\nLet's guard this precious dream with grace\n\n[Bridge]\nThrough the ashes, we will rise\nHand in hand, towards peaceful skies\nNo more sorrow, no more pain\nTogether, we'll break these chains\n\n[Chorus]\nSilent battlegrounds, no birds' song\nShadows of war, where we don't belong\nMay flowers of peace bloom in this place\nLet's guard this precious dream with grace\n\n[<PERSON>ro]\nIn unity, our strength will grow\nA brighter future, we'll soon know\nFrom the ruins, hope will spring\nA new dawn, we'll together bring"}, "tags": {"type": "string", "description": "Music genre", "example": "pop metal male melancholic"}, "title": {"type": "string", "description": "Music title", "example": "Silent Battlefield"}, "make_instrumental": {"type": "boolean", "description": "Whether to generate instrumental music", "example": "false"}, "wait_audio": {"type": "boolean", "description": "Whether to wait for music generation, default is false, directly return audio task information; set to true, will wait for up to 100s until the audio is generated.", "example": "false"}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["0", "1"], "properties": [{"$ref": "#/components/schemas/audio_info"}, {"$ref": "#/components/schemas/audio_info"}]}}}}}}}}, "/api/generate_lyrics": {"post": {"summary": "Generate lyrics based on Prompt.", "description": "Generate lyrics based on Prompt.", "tags": ["default"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["prompt"], "properties": {"prompt": {"type": "string", "description": "Prompt", "example": "A soothing lullaby"}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "object", "properties": {"text": {"type": "string", "description": "Lyrics"}, "title": {"type": "string", "description": "music title"}, "status": {"type": "string", "description": "Status"}}}}}}}}}, "/api/get": {"get": {"summary": "Get audio information", "description": "", "tags": ["default"], "parameters": [{"in": "query", "name": "ids", "description": "Audio IDs, separated by commas. Leave blank to return a list of all music.", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["0", "1"], "properties": [{"$ref": "#/components/schemas/audio_info"}, {"$ref": "#/components/schemas/audio_info"}]}}}}}}}}, "/api/get_limit": {"get": {"summary": "Get quota information.", "description": "", "tags": ["default"], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "object", "required": ["credits_left", "period", "monthly_limit", "monthly_usage"], "properties": {"credits_left": {"type": "number", "description": "Remaining credits,Each generated audio consumes 5 credits."}, "period": {"type": "string", "description": "Period"}, "monthly_limit": {"type": "number", "description": "Monthly limit"}, "monthly_usage": {"type": "number", "description": "Monthly usage"}}}}}}}}}}, "components": {"schemas": {"audio_info": {"type": "object", "required": ["id", "title", "image_url", "lyric", "audio_url", "video_url", "created_at", "model_name", "status", "gpt_description_prompt", "prompt", "type", "tags"], "properties": {"id": {"type": "string", "description": "audio id"}, "title": {"type": "string", "description": "music title"}, "image_url": {"type": "string", "description": "music cover image"}, "lyric": {"type": "string", "description": "music lyric"}, "audio_url": {"type": "string", "description": "music download url"}, "video_url": {"type": "string", "description": "Music video download link, can be used to share"}, "created_at": {"type": "string", "description": "Create time"}, "model_name": {"type": "string", "description": "suno model name, chirp-v3"}, "status": {"type": "string", "description": "The generated states include submitted, queue, streaming, complete."}, "gpt_description_prompt": {"type": "string", "description": "Simple mode on user input prompt, <PERSON><PERSON> will generate formal prompts, lyrics, etc."}, "prompt": {"type": "string", "description": "The final prompt for executing the generation task, customized by the user in custom mode, automatically generated by <PERSON><PERSON> in simple mode."}, "type": {"type": "string", "description": "Type"}, "tags": {"type": "string", "description": "Music genre. User-provided in custom mode, automatically generated by <PERSON><PERSON> in simple mode."}}, "title": "audio_info", "description": "Audio Info"}}}}