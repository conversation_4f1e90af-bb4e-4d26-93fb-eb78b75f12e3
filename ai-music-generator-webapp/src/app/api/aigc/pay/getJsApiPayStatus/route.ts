import logger from "@/lib/logger";
import { respData, respErr } from "@/lib/resp";
import { findOrderByOrderId } from "@/models/order";

export async function POST(req: Request) {
  try {
    // 验证参数
    const api_id = req.headers.get("x-apiid") || "";
    const token = req.headers.get("x-token") || "";
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }
    const body = await req.json();
    logger.info(`---orderNotify req=`, body);
    const orderId = body.orderId;
    if (!orderId) {
      return respErr("Invalid params");
    }
    // find order by orderId
    const order = await findOrderByOrderId(orderId);
    if (!order) {
      return respErr("empty order");
    }
    const data = {
      payStatus: order.payStatus,
    };
    logger.info(`---getJsApiPayStatus rsp=${JSON.stringify(data)}`);
    return respData(data);
  } catch (error) {
    logger.error("getJsApiPayStatus error=", error);
    return respErr("getJsApiPayStatus error");
  }
}
