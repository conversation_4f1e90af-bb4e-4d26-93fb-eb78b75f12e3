import WxPay from "wechatpay-node-v3";
import { Buffer } from "buffer";

// 从环境变量中获取 appid 和 mchid
const appid = process.env.APP_ID;
const mchid = process.env.MCH_ID;
const cfgPublicKeyBase64 = process.env.PUBLIC_KEY_BASE64;
const cfgPrivateKeyBase64 = process.env.PRIVATE_KEY_BASE64;
const key = process.env.APIV3_KEY;
export const notifyUrl = process.env.NOTIFY_URL;

// 确保环境变量已经正确设置
if (
  !appid ||
  !mchid ||
  !cfgPublicKeyBase64 ||
  !cfgPrivateKeyBase64 ||
  !key ||
  !notifyUrl
) {
  console.error(
    "ERROR: Check Wechat configuration environment variables. Someone required is miss!"
  );
  process.exit(1); // 退出程序，因为没有必要的配置信息
}

// 从环境变量中读取Base64编码的密钥
const publicKey = Buffer.from(cfgPublicKeyBase64, "base64");
const privateKey = Buffer.from(cfgPrivateKeyBase64, "base64");

const pay = new WxPay({
  appid: appid, // 直连商户申请的公众号或移动应用 appid
  mchid: mchid, // 商户号
  publicKey: publicKey, // 公钥
  privateKey: privateKey, // 私钥
  key: key, // APIv3密钥
});

export default pay;
