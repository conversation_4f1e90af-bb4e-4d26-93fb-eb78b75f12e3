import { getDb } from "@/models/db";
import { UserAuth } from "@/types/user_auth";
import { PoolClient, QueryResultRow } from "pg";

export async function findUserAuthByUserId(userId: string): Promise<UserAuth | undefined> {
  const db = getDb();
  const query = `SELECT * FROM user_auths WHERE user_id = $1`;
  const params = [userId];

  try {
    const res = await db.query(query, params);
    if (res.rowCount === 0) {
      return undefined;
    }
    return formatUserAuth(res.rows[0]);
  } catch (err) {
    console.error('Error retrieving user authentication:', err);
    throw err;  // Rethrow or handle as appropriate for your application
  }
}

export async function insertUserAuth(user_auth: UserAuth) {
  const created_at: string = new Date().toISOString();

  const db = await getDb();
  const res = await (db.query as any)(
    `INSERT INTO user_auths 
      (user_id, auth_type, open_id, credential, union_id, created_at) 
      VALUES 
      ($1, $2, $3, $4, $5, $6)
  `,
    [
      user_auth.user_id,
      user_auth.auth_type,
      user_auth.open_id,
      user_auth.credential,
      user_auth.union_id,
      created_at,
    ]
  );
  return res;
}

export async function insertUserAuthTx(user_auth: UserAuth, db: PoolClient) {
  const created_at: string = new Date().toISOString();
  const res = (db.query as any)(
    `INSERT INTO user_auths
      (user_id, auth_type, open_id, credential, union_id, created_at)
      VALUES
      ($1, $2, $3, $4, $5, $6)
    `,
    [
      user_auth.user_id,
      user_auth.auth_type,
      user_auth.open_id,
      user_auth.credential,
      user_auth.union_id,
      created_at,
    ]
  );
  return res;
}

export async function findUserAuthByOpenId(
  open_id: string
): Promise<UserAuth | undefined> {
  const db = getDb();
  const res = await db.query(
    `SELECT * FROM user_auths WHERE open_id = $1 LIMIT 1`,
    [open_id]
  );
  if (res.rowCount === 0) {
    return undefined;
  }

  const { rows } = res;
  const row = rows[0];
  const user_auth: UserAuth = {
    id: row.id,
    user_id: row.user_id,
    auth_type: row.auth_type,
    open_id: row.open_id,
    credential: row.credential,
    union_id: row.union_id,
    created_at: row.created_at,
    updated_at: row.updated_at,
  };

  return user_auth;
}

function formatUserAuth(row: QueryResultRow): UserAuth {
  const userAuth: UserAuth = {
    id: row.id,
    user_id: row.user_id,
    auth_type: row.auth_type,
    open_id: row.open_id,
    credential: row.credential,
    union_id: row.union_id,
    created_at: row.created_at,
    updated_at: row.updated_at,
  };
  return userAuth;
}
