var t = require('../../@babel/runtime/helpers/toConsumableArray'),
  e = require('./../../utils/util.js'),
  a = require('./../../services/index'),
  o = '7tqzVg9jQjEM-cIi2Yg0ZWkYe4j9tDrWRpi_1Y7oEWw';
Page({
  data: {
    isLogin: !1,
    songDetail: {},
    dialogShow: !1,
    operateShow: !1,
    dialogShow2: !1,
    duration: 500,
    position: 'bottom',
    round: !0,
    overlay: !0,
    customStyle: 'background:#19191a;height:330px',
    overlayStyle: '',
    userInfo: { headerUrl: './../../image/icon45.svg' },
    myList: [],
    listQuery: { page: 1, pageSize: 30, from: 'mini' },
    pages: 1,
    reachBottom: !1,
    vipCount: {},
    tabIndex: 1,
    generatingList: [],
    taskIdArr: [],
    internal: null,
    refreshTips: '',
    scrollTop: 0,
    scrollH: 0,
    triggered: !1,
    successMsg: '',
    errorMsg: '',
    isPay: !0,
    iosPay: !0,
  },
  onLoad: function () {
    var t = this;
    wx.hideShareMenu({}),
      wx.getSystemInfo({
        success: function (e) {
          t.setData({ scrollH: e.windowHeight - 260 });
        },
      }),
      wx.getStorageSync('userId') && this.setData({ isLogin: !0 });
  },
  onShow: function () {
    var e = this,
      a = wx.getSystemInfoSync();
    'ios' === a.platform
      ? this.setData({ isPay: !1 })
      : 'android' === a.platform && this.setData({ isPay: !0 }),
      wx.setNavigationBarTitle({ title: '个人中心' }),
      'function' == typeof this.getTabBar &&
        this.getTabBar() &&
        this.getTabBar().setData({ selected: 2 });
    var o = wx.getStorageSync('generateId');
    o &&
      this.setData(
        { tabIndex: 2, taskIdArr: [].concat(t(this.data.taskIdArr), [o]) },
        function () {
          e.onQuery(), e.onInternal();
        },
      ),
      wx.getStorageSync('accessToken') &&
        (this.getUser(),
        (this.data.listQuery.page = 1),
        (this.data.myList = []),
        this.getMyList(),
        this.onGetRemainCount(),
        o
          ? wx.removeStorage({ key: 'generateId' })
          : this.getlistRunningTaskIds());
  },
  onHide: function () {
    clearInterval(this.data.internal), this.setData({ internal: null });
  },
  onShareAppMessage: function (t) {
    wx.showShareMenu({ withShareTicket: !0 });
    var e = t.target.dataset.item;
    return {
      title: e.title,
      path: '/pages/play/play?clipId='.concat(e.music_id),
      imageUrl: e.image_url,
      success: function (t) {
        console.log(t);
      },
      fail: function () {
        console.log('--- 转发失败 ---', path);
      },
    };
  },
  scribeMessage: function () {
    wx.requestSubscribeMessage({
      tmplIds: [o],
      success: function (t) {
        'accept' === t[o]
          ? console.log('用户接受订阅')
          : console.log('用户拒绝订阅');
      },
      fail: function (t) {
        console.log('订阅失败', t);
      },
    });
  },
  openLogin: function () {
    wx.navigateTo({ url: '/pages/login/login' });
  },
  onGetRemainCount: function () {
    var t = this;
    a.getRemainCount({}).then(function (e) {
      0 == e.code
        ? (t.setData({ vipCount: e.data }),
          e.data.monthCoinDate &&
            new Date().getTime() >
              new Date(e.data.monthCoinDate).getTime() + 864e5 &&
            t.setData({ isOverdue: !0 }))
        : t.setData({ errorMsg: e.msg });
    });
  },
  getUser: function () {
    var t = this;
    a.getUser().then(function (e) {
      0 === e.code &&
        (wx.setStorageSync('userInfo', e.data),
        t.setData({ isLogin: !0, userInfo: e.data, iosPay: e.data.iosPay }));
    });
  },
  onPulling: function () {
    console.log('自定义下拉刷新被触发'),
      this.setData({ refreshTips: '松开刷新我的作品' });
  },
  onRestore: function (t, e) {
    console.log('onRestore 自定义下拉刷新被复位'),
      this.setData({ refreshTips: '' });
  },
  onAbort: function () {
    console.log('onAbort 自定义下拉刷新被中止'),
      this.setData({ refreshTips: '' });
  },
  onRefresh: function () {
    var t = this;
    this.setData(
      { triggered: !0, refreshTips: '', reachBottom: !1 },
      function () {
        (t.data.listQuery.page = 1),
          (t.data.myList = []),
          t.getMyList(),
          t.setData({ triggered: !1 });
      },
    );
  },
  onReachBottom: function () {
    var t = this.data,
      e = t.pages;
    t.listQuery.page <= e
      ? (this.data.listQuery.page++, this.getMyList())
      : this.setData({ reachBottom: !0 });
  },
  onLogout: function () {
    wx.removeStorageSync('userInfo'),
      wx.removeStorageSync('accessToken'),
      wx.removeStorageSync('userId'),
      wx.removeStorageSync('loginState'),
      wx.removeStorageSync('code'),
      wx.removeStorageSync('ops'),
      this.setData({
        imgList: [],
        col1: [],
        col2: [],
        isLogin: !1,
        userInfo: { headerUrl: './../../image/icon45.svg' },
      });
  },
  openVipPage: function () {
    wx.navigateTo({ url: '/pages/account/account' });
  },
  onScroll: function (t) {
    t.detail.scrollTop > 200
      ? this.setData({ active: !0 })
      : this.setData({ active: !1 });
  },
  onGetTop: function () {
    this.setData({ scrollTop: 0 });
  },
  getMyList: function () {
    var t = this,
      e = this.data.myList;
    a.myList(this.data.listQuery).then(function (a) {
      0 === a.code &&
        t.setData({ pages: a.data.pages, myList: e.concat(a.data.records) });
    });
  },
  tabChange: function (t) {
    var e = t.currentTarget.dataset.index;
    this.setData({ tabIndex: e }),
      (this.data.listQuery.page = 1),
      (this.data.myList = []),
      this.getMyList();
  },
  onInternal: function () {
    var t = this;
    this.data.internal = setInterval(function () {
      t.data.taskIdArr.length > 0
        ? t.onQuery()
        : clearInterval(t.data.internal);
    }, 5e3);
  },
  onQuery: function () {
    var t = this,
      e = this.data.taskIdArr;
    a.batchQuery({ ids: e, from: 'mini' }).then(function (a) {
      if (0 === a.code) {
        var o = [];
        a.data.forEach(function (a) {
          if (
            (a.list.forEach(function (t, e) {
              o.push(t);
            }),
            t.setData({ generatingList: o }),
            30 == a.status)
          ) {
            var n = e.indexOf(a.id);
            e.splice(n, 1);
          }
          if (40 == a.status) {
            var i = e.indexOf(a.id);
            e.splice(i, 1);
          }
        }),
          0 == e.length &&
            (t.onGetRemainCount(),
            (t.data.myList = []),
            (t.data.listQuery.page = 1),
            t.getMyList(),
            clearInterval(t.data.internal),
            t.setData({ tabIndex: 1, generatingList: [] }));
      }
    });
  },
  getlistRunningTaskIds: function () {
    var t = this;
    a.listRunningTaskIds({}).then(function (e) {
      e.data
        ? t.setData({ taskIdArr: e.data }, function () {
            t.onInternal();
          })
        : t.setData({ generatingList: [] });
    });
  },
  onChildEvent: function (t) {
    this.setData({
      dialogShow: !0,
      operateShow: !0,
      position: 'bottom',
      songDetail: t.detail,
    });
  },
  onCreate: function (t) {
    wx.switchTab({ url: '/pages/index/index' }),
      this.setData({ dialogShow: !1, operateShow: !1 }),
      wx.setStorageSync('songDetail', this.data.songDetail),
      wx.setStorageSync('createAction', t.currentTarget.dataset.action);
  },
  onSureDelete: function (t) {
    var e = this;
    a.deleteSong({ id: this.data.songDetail.id }).then(function (t) {
      0 == t.code &&
        ((e.data.listQuery.page = 1), (e.data.myList = []), e.getMyList());
    }),
      this.setData({ dialogShow: !1, operateShow: !1, dialogShow2: !1 });
  },
  onDelete: function () {
    this.setData({ dialogShow2: !0 });
  },
  onCancelDialog: function () {
    this.setData({ dialogShow2: !1 });
  },
  onWhole: function () {
    var e = this;
    a.generate({
      action: 'whole',
      // continueClipId: this.data.songDetail.clipId,
      continueClipId: this.data.songDetail.music_id,
    }).then(function (a) {
      0 == a.code &&
        e.setData(
          {
            dialogShow: !1,
            operateShow: !1,
            taskIdArr: [].concat(t(e.data.taskIdArr), [a.data.id]),
            tabIndex: 2,
          },
          function () {
            e.onQuery(), e.onInternal();
          },
        );
    });
  },
  onDownloadVideo: function () {
    (0, e.downloadImg)(this.data.songDetail.oss_video_url), //request调用，需要oss备案域名
      this.setData({ dialogShow: !1, operateShow: !1 });
  },
  onDownloadAudio: function () {
    var t = this;
    wx.setClipboardData({
      data: t.data.songDetail.audio_url, //粘贴外部打开，可用原链接
      success: function (e) {
        wx.getClipboardData({
          success: function (e) {
            wx.showToast({ title: '内容已复制', icon: 'none' }),
              wx.showModal({
                title: '',
                showCancel: !1,
                content: '已复制下载链接，打开浏览器粘贴下载',
                success: function (t) {
                  t.confirm
                    ? console.log('用户点击确定')
                    : t.cancel && console.log('用户点击取消');
                },
              }),
              t.setData({ dialogShow: !1, operateShow: !1 });
          },
        });
      },
    });
  },
  openCustomer: function () {
    wx.navigateTo({ url: '/pages/customer/customer' });
  },
});
