const STATUS = {
  LOADING: 1,
  CODE_GOT: 2,
  USER_REJECT: 3,
};
const ENV_VERSION_MAP = {
  DEVELOP: 'develop',
  TRIAL: 'trial',
  RELEASE: 'release',
};
const version = '20230602';const mobileAppId = "";const shellAppId = 'wx9487a19f6505e47f';let transitiveData = '';Page({
  options: {
    styleIsolation: 'page-apply-shared'
  },
  data: {
    status: STATUS.LOADING,
    appName: "Suno AI创作",
    appIcon: "http://wx.qlogo.cn/mmhead/Iic9WLWEQMg2Fzic2exbqUqr6F1FYC7uTeNiahFEO10905zVxpmfniaISMyYVyIynWrFUWUbKibxzIcA/0",
    hasBindOpen: mobileAppId.startsWith('wx'),
  },
  onLoad(res) {
    wx.setNavigationBarTitle({
      title: this.data.appName
    });
    // 安卓带有 transitiveData
    if (JSON.stringify(res) !== '{}') {
      transitiveData = res;
    }
  },
  onShow() {
    console.log('[PAGE VERSION] ', version);
    this.checkReferrerInfo();
  },
  checkReferrerInfo() {
    console.log('checkReferrerInfo')
    const { envVersion } = wx.getAccountInfoSync().miniProgram
    const { referrerInfo } = wx.getEnterOptionsSync()
    const referrerAppId = referrerInfo.appId || 'NOT_KNOWN';
    console.log('[PAGE Referrer] ', envVersion, referrerInfo);

    // 非正式版可以直接显示授权按钮
    if ([ENV_VERSION_MAP.DEVELOP, ENV_VERSION_MAP.TRIAL].includes(envVersion)) {
			this.wxlogin();
			console.log('checkReferrrerInfo includes envVersion')
      return
    }

    // 正式版要校验来源是否合法
    if ([mobileAppId, shellAppId].includes(referrerAppId)) {
			console.log('checkReferrrerInfo includes referrerAppId')
      this.wxlogin()
    }

		console.log('checkReferrerInfo failed')
  },
  wxlogin() {
    wx.login({
      complete: (res) => {
				console.log('wx.login complete', res)
        if (res.errMsg === 'login:ok') {
          const data = {
            data: res.code,
          }
          if (transitiveData) {
            data.transitiveData = transitiveData
          }
          this.setData({
            code: res.code,
            status: STATUS.CODE_GOT,
            dataStr: JSON.stringify(data)
          })
        } else {
          this.setData({
            status: STATUS.USER_REJECT,
          })
          console.error(`${res.err_code || 'err_code not found'}: ${res.errMsg || 'errMsg not found'}`)
        }
      }
    })
  },
  loginFailed() {
		console.error('loginFailed', '获取失败')
    wx.showToast({
      title: '获取失败',
      duration: 1000,
      icon: 'error',
      mask: false,
    })

    this.wxlogin()
  },
  launchAppError(e) {
		console.error('launchAppError', e)
    setTimeout(() => {
      wx.showModal({
        title: e.detail.errMsg,
        icon: 'error'
      });
    }, 1000)
  },
  navigateBackToHome() {
		console.log('navigateBackToHome', transitiveData)
    let url = ''
    if (transitiveData && transitiveData.redirectPath) {
      url = transitiveData.redirectPath
    } else if (__wxConfig && __wxConfig.pages && __wxConfig.pages.length) {
      url = '/' + __wxConfig.pages[0]
    }
     
    if (url) {
      setTimeout(() => {
        wx.redirectTo({
          url
        })
      }, 500)
    }
  },
})