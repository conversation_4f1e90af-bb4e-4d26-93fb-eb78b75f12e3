<view class="nav-wrap" style="padding-top: {{barHeightrpx}}rpx; background-image:{{navbarData.bg}}">
    <view class="nav-capsule" style="width:{{lWidth}}px;" wx:if="{{navbarData.showCapsule}}">
        <view bindtap="{{navbarData.goHome?'goHome':navbarData.showTip?'showTip':'_navback'}}" class="back-box" wx:if="{{navbarData.showBack}}">
            <view class="iconfont icon-back"></view>
        </view>
        <view class="song_box">
            <text bindtap="{{navbarData.goHome?'goHome':navbarData.showTip?'showTip':'_navback'}}" class="title-txt">{{navbarData.title}}</text>
            <view bindtap="showHomeTip" class="homeTip" wx:if="{{navbarData.showW<PERSON>hao}}">
                <view class="iconfont icon-wenhao"></view>
            </view>
        </view>
    </view>
</view>
<view wx:if="{{navbarData.showHomeTip}}">
    <view>
        <image bindtap="btn_img" src="{{tishi_img}}" style="width:{{windowWidth}}px;height:{{windowHeight}}px;position:fixed;top:0;z-index:999;"></image>
    </view>
</view>
