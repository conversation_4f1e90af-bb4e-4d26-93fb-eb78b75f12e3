import { User } from "@/types/user";
import { getDb } from "@/models/db";
import { PoolClient, QueryResultRow } from "pg";

export async function updateUserIsBuyLimitOnce(
  userId: string,
  isBuyLimitOnce: boolean,
) {
  const updated_at: string = new Date().toISOString();
  const db = getDb();
  const query = `
  UPDATE users SET is_buy_limit_once=$2, updated_at=$3 
  WHERE user_id=$1`;
  const params = [userId, isBuyLimitOnce, updated_at];
  try {
    const result = await db.query(query, params);
    if (result.rowCount === 0) {
      throw new Error(`User with userId=${userId} not found.`);
    }
    console.info(`User ${userId} updated ${isBuyLimitOnce} successfully.`);
    return true;
  } catch (error) {
    console.error("Error in updateUserIsBuyLimitOnce", { query, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error updateUserIsBuyLimitOnce ${error}`); // 抛出错误，以供上层处理
  }
}

export async function insertUser(user: User) {
  const createdAt: string = new Date().toISOString();

  const db = await getDb();
  const res = await (db.query as any)(
    `INSERT INTO users 
      (user_id, nickname, avatar_url, phone, created_at) 
      VALUES 
      ($1, $2, $3, $4, $5)
  `,
    [user.user_id, user.nickName, user.headerUrl, user.phone, createdAt]
  );
  return res;
}

export async function insertUserTx(user: User, db: PoolClient) {
  const createdAt: string = new Date().toISOString();
  const res = await (db.query as any)(
    `INSERT INTO users
      (user_id, nickname, avatar_url, phone, created_at)
      VALUES
      ($1, $2, $3, $4, $5)
    `,
    [user.user_id, user.nickName, user.headerUrl, user.phone, createdAt]
  );
  return res;
}

export async function findUserByPhone(
  phone: string
): Promise<User | undefined> {
  const db = getDb();
  const res = await db.query(`SELECT * FROM users WHERE phone = $1 LIMIT 1`, [
    phone,
  ]);
  if (res.rowCount === 0) {
    return undefined;
  }

  const { rows } = res;
  const row = rows[0];
  // const user: User = {
  //   id: row.id,
  //   user_id: row.user_id,
  //   email: row.email,
  //   nickName: row.nickname,
  //   headerUrl: row.avatar_url,
  //   createdAt: row.created_at,
  //   phone: row.phone,
  //   coin: row.coin,
  //   maxRunningJob: row.max_running_job,
  //   subscriber: row.subscriber,
  //   bindEmail: row.bind_email,
  //   sensitiveFlag: row.sensitive_flag,
  //   setPassword: row.set_password,
  //   iosPay: row.ios_pay,
  // };

  const user = formatUser(row);

  return user;
}

// Deprecated
export async function findUserById(id: number): Promise<User | undefined> {
  const db = getDb();
  const res = await db.query(`SELECT * FROM users WHERE id = $1 LIMIT 1`, [id]);
  if (res.rowCount === 0) {
    return undefined;
  }

  const { rows } = res;
  const row = rows[0];
  const user: User = {
    id: row.id,
    user_id: row.user_id,
    email: row.email,
    nickName: row.nickname,
    headerUrl: row.avatar_url,
    createdAt: row.created_at,
    phone: row.phone,
    coin: row.coin,
    maxRunningJob: row.max_running_job,
    subscriber: row.subscriber,
    bindEmail: row.bind_email,
    sensitiveFlag: row.sensitive_flag,
    setPassword: row.set_password,
    iosPay: row.ios_pay,
  };

  return user;
}

export async function findUserByUserId(
  user_id: string
): Promise<User | undefined> {
  const db = getDb();
  const res = await db.query(`SELECT * FROM users WHERE user_id = $1 LIMIT 1`, [
    user_id,
  ]);
  if (res.rowCount === 0) {
    return undefined;
  }

  const { rows } = res;
  const row = rows[0];
  const user = formatUser(row);

  return user;
}

function formatUser(row: QueryResultRow): User {
  const user: User = {
    id: row.id,
    user_id: row.user_id,
    email: row.email,
    nickName: row.nickname,
    headerUrl: row.avatar_url,
    createdAt: row.created_at,
    phone: row.phone,
    coin: row.coin,
    maxRunningJob: row.max_running_job,
    subscriber: row.subscriber,
    bindEmail: row.bind_email,
    sensitiveFlag: row.sensitive_flag,
    setPassword: row.set_password,
    iosPay: row.ios_pay,
    isBuyLimitOnce: row.is_buy_limit_once,
  };

  return user;
}
