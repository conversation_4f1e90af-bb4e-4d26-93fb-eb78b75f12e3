import { respErr } from "@/lib/resp";

// 审核用控制是否展示【发现】跳转主页
export async function POST(req: Request) {
  try {
    // get switch from env
    const num_audit_switch = Number(process.env.AUDIT_SWITCH);
    let audit_switch;
    if (num_audit_switch == 1) {
      audit_switch = true;
    } else {
      audit_switch = false;
    }
    // console.log(`AUDIT_SWITCH=${process.env.AUDIT_SWITCH}`);
    // console.log(`num_audit_switch=${num_audit_switch}`);
    // console.log(`audit_switch=${audit_switch}`);

    // 审核开关 TRUE=审核中，不显示风险页 FALSE=已审核，完全显示
    let json = {
      code: 0,
      msg: "OK",
      data: audit_switch,
    };
    return Response.json(json);
  } catch (error) {
    console.error("switchHome error=", error);
    return respErr("switchHome error");
  }
}
