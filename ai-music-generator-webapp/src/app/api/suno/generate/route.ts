import { genUuid } from "@/lib/index";
import { AudioInfo, sunoApi } from "@/lib/SunoApi";
import { respData, respErr } from "@/lib/resp";
import { insertMusic } from "@/models/music";
import { getUserCredits } from "@/services/order";
import { saveTask } from "@/services/task";
import { Music } from "@/types/music";
import { Task } from "@/types/task";
import { updateTaskGenerate } from "@/models/task";
import logger from "@/lib/logger";
import { sleep } from "@/lib/utils";

export async function POST(req: Request) {
  try {
    const startTime = Date.now();
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const body = await req.json();
    console.log(`---generate req=${JSON.stringify(body)}`);

    const {
      inputType,
      idea,
      lyric,
      style,
      title,
      makeInstrumental,
      wait_audio,
    } = body;

    const user_credits = await getUserCredits(user_id);
    if (!user_credits || user_credits.leftCoin < 1) {
      return respErr("剩余的积分已不够，充值可获得更多积分");
    }

    // 创建任务记录
    const task_id = genUuid();
    const task: Task = {
      task_id: task_id,
      user_id: user_id,
      music_ids: [], //待更新
      // clips: "", //待更新
      batch_size: 2,
      status: "queued",
    };
    await saveTask(task);

    // 启动异步任务来处理音频生成和音乐插入
    handleMusicGeneration(
      task_id,
      user_id,
      inputType,
      idea,
      lyric,
      style,
      title,
      makeInstrumental,
      wait_audio
    ).catch((error) => {
      logger.error("Failed to handle music generation:", error);
      // 可能需要更新任务状态为失败
    });

    const costTime = Date.now() - startTime;
    logger.info(`---generate rsp=${task_id}, costTime=${costTime}`);
    await sleep(2, 3); // Wait for 2~3 seconds before response
    return respData({ id: task_id });
  } catch (error) {
    console.error("generate error=", error);
    return respErr("generate error");
  }
}

async function handleMusicGeneration(
  task_id: string,
  user_id: string,
  inputType: number,
  idea?: string,
  lyric?: string,
  style?: string,
  title?: string,
  makeInstrumental?: boolean,
  wait_audio?: boolean
) {
  logger.info("---call handleMusicGeneration");
  let audioInfos: AudioInfo[] = [];

  if (inputType === 10 && idea) {
    audioInfos = await (
      await sunoApi
    ).generate(idea, makeInstrumental === true, wait_audio === true);
  } else if (inputType === 20 && lyric && style && title) {
    audioInfos = await (
      await sunoApi
    ).custom_generate(
      lyric,
      style,
      title,
      makeInstrumental === true,
      wait_audio === true
    );
  }

  let music_ids: string[] = [];
  audioInfos.forEach(async (audioInfo) => {
    music_ids.push(audioInfo.id);
    const music: Music = {
      music_id: audioInfo.id,
      user_id: user_id,
      video_url: audioInfo.video_url,
      audio_url: audioInfo.audio_url,
      image_url: audioInfo.image_url,
      model_name: audioInfo.model_name,
      status: audioInfo.status,
      title: audioInfo.title,
      created_at: audioInfo.created_at,
      music_metadata: {
        music_id: audioInfo.id,
        user_id: user_id,
        tags: audioInfo.tags,
        prompt: audioInfo.prompt,
        gpt_description_prompt: audioInfo.gpt_description_prompt,
        type: audioInfo.type,
        duration: Number(audioInfo.duration),
        created_at: audioInfo.created_at,
      },
    };
    await insertMusic(music);
  });
  logger.info(`---audioInfos=${audioInfos}, music_ids=${music_ids}`);
  // 更新任务中的数据
  await updateTaskGenerate(task_id, audioInfos, music_ids);
  logger.info("---call handleMusicGeneration end");
}
