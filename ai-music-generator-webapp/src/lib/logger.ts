// logger.ts
import { createLogger, format, transports } from "winston";
import * as path from "path";
import DailyRotateFile from "winston-daily-rotate-file";

// 根据环境变量判断是否为开发环境
const isDevelopment = process.env.NODE_ENV === "development";
// console.log(`Environment: ${process.env.NODE_ENV}`);
// console.log("Is Development:", isDevelopment);

// 使用 process.cwd() 获取项目根目录，确保日志文件在根目录下的 logs 文件夹中
const logsDir = path.join(process.cwd(), "logs");
// console.log(`Logs Directory: ${logsDir}`);

const fileDatePattern = "YYYYMMDD";
const fileSizeLimit = 5 * 1024 * 1024; // 5MB
const maxFilesDay = "3d"; // 保留3天的日志文件

const logger = createLogger({
  // 如果非开发环境，设置为只使用控制台日志，不记录到文件
  transports: isDevelopment
    ? [
        new DailyRotateFile({
          level: "info",
          filename: path.join(logsDir, "info-%DATE%.log"),
          datePattern: fileDatePattern,
          maxSize: fileSizeLimit,
          maxFiles: maxFilesDay,
          zippedArchive: true,
        }),
        new DailyRotateFile({
          level: "warn",
          filename: path.join(logsDir, "warn-%DATE%.log"),
          datePattern: fileDatePattern,
          maxSize: fileSizeLimit,
          maxFiles: maxFilesDay,
          zippedArchive: true,
        }),
        new DailyRotateFile({
          level: "error",
          filename: path.join(logsDir, "error-%DATE%.log"),
          datePattern: fileDatePattern,
          maxSize: fileSizeLimit,
          maxFiles: maxFilesDay,
          zippedArchive: true,
        }),
        new transports.Console({
          level: "debug",
          format: format.combine(format.colorize(), format.simple()),
        }),
      ]
    : [
        new transports.Console({
          level: "debug",
          format: format.combine(format.colorize(), format.simple()),
        }),
      ],
  format: format.combine(
    format.timestamp({ format: "YYYY-MM-DDTHH:mm:ss.SSSZ" }),
    format.printf(
      (info) => `${info.timestamp} [${info.level}]: ${info.message}`
    )
  ),
  exceptionHandlers: [new transports.File({ filename: "exceptions.log" })],
});

logger.on("error", function (err) {
  console.log("Logging error:", err);
});

export default logger;
