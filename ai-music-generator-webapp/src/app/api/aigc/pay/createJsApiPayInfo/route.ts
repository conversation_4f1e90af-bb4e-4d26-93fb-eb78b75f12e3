import { genUuid, getOutTradeNo } from "@/lib";
import logger from "@/lib/logger";
import { respData, respErr } from "@/lib/resp";
import wxpay, { notifyUrl } from "@/lib/wxpay";
import { insertOrder } from "@/models/order";
import { findSubscribePackageCfgById } from "@/models/subscribe_package_cfg";
import { findUserAuthByUserId } from "@/models/user_auth";
import { Order } from "@/types/order";
import { PayType, PayStatus, PeriodType } from "@/types/common";
import { updateUserIsBuyLimitOnce } from "@/models/user";

/**
 * create pay info
 *
 * @returns list
 */
export async function POST(req: Request) {
  try {
    // 验证参数
    const api_id = req.headers.get("x-apiid") || "";
    const token = req.headers.get("x-token") || "";
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }
    const body = await req.json();
    logger.info(`---createJsApiPayInfo req=`, body);
    const apiPackageId = body.apiPackageId;
    if (!apiPackageId) {
      return respErr("Invalid params");
    }

    // 根据 `packageId` 查询订阅包信息
    const subscribePackageCfg = await findSubscribePackageCfgById(apiPackageId);
    logger.debug("subscribePackageCfg=", subscribePackageCfg);
    if (!(subscribePackageCfg && subscribePackageCfg.amount)) {
      logger.error(
        "Error findSubscribePackageCfgById packageId=",
        apiPackageId
      );
      return respErr("订阅包信息异常");
    }
    // 根据 user_id 查询 open_id
    const userAuth = await findUserAuthByUserId(user_id);
    if (!(userAuth && userAuth.open_id)) {
      logger.error("Error findUserAuthByUserId user_id=", user_id);
      return respErr("用户授权信息异常");
    }

    // 生成商户订单号
    const outTradeNo = getOutTradeNo(user_id);
    const orderId = genUuid();

    const currentDate = new Date();
    let expiryDate: Date | null = new Date(currentDate.getTime()); // 初始化expiryDate为当前日期

    // 根据 subscribePackageCfg.period 设置到期时间
    switch (subscribePackageCfg.period) {
      case PeriodType.Monthly:
        expiryDate.setMonth(expiryDate.getMonth() + 1);
        break;
      case PeriodType.Quarterly:
        expiryDate.setMonth(expiryDate.getMonth() + 3);
        break;
      case PeriodType.Yearly:
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);
        break;
      case PeriodType.Once:
        // 一次性订阅，根据业务需求不设置到期日期
        expiryDate = null;
        break;
      default:
        throw new Error("Invalid period specified");
    }

    const created_at = currentDate.toISOString();
    const expired_at = expiryDate ? expiryDate.toISOString() : null; // 如果expiryDate为null，expired_at也应为null

    // 生成平台订单
    const order: Order = {
      orderId: orderId,
      userId: user_id,
      packageId: apiPackageId,
      outTradeNo: outTradeNo,
      payType: PayType.WxPay,
      payStatus: PayStatus.Created,
      amount: subscribePackageCfg.amount,
      currency: subscribePackageCfg.currency,
      period: subscribePackageCfg.period,
      credits: subscribePackageCfg.credits,
      createdAt: created_at,
      expiredAt: expired_at,
    };
    await insertOrder(order);
    logger.info("create new order: ", order);

    let params = {
      description: subscribePackageCfg.packageName,
      out_trade_no: outTradeNo,
      notify_url: notifyUrl!,
      amount: {
        total: subscribePackageCfg.amount, // 单位为分
        currency: subscribePackageCfg.currency,
      },
      payer: {
        openid: userAuth.open_id,
      },
    };
    // logger.debug("transactions_jsapi params=", params);
    // 生成预付单
    // 生成带签名支付信息
    const result = await wxpay.transactions_jsapi(params);
    // 返回支付参数
    // logger.debug("transactions_jsapi result=", result);
    if (result.status != 200) {
      logger.error("transactions_jsapi error=", JSON.stringify(result));
      return respErr("transactions_jsapi error=" + result.error);
    }
    // ktodo update when orderNotify paied
    if (subscribePackageCfg.isLimitOnce === true) {
      // update user.isBuy = true
      await updateUserIsBuyLimitOnce(user_id, true);
    }
    // Combine result.data with orderId and return
    const data = {
      ...result.data,
      orderId: orderId,
    };
    logger.info(`---createJsApiPayInfo rsp=${JSON.stringify(data)}`);
    return respData(data);
  } catch (error) {
    logger.error("createJsApiPayInfo error=", error);
    return respErr("createJsApiPayInfo error");
  }
}
