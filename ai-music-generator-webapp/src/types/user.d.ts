export interface User {
  id?: number;
  user_id: string;
  email?: string;
  nickName?: string;
  headerUrl?: string;
  phone?: string;
  coin?: string;
  maxRunningJob?: number;
  subscriber?: boolean;
  bindEmail?: string;
  sensitiveFlag?: number;
  setPassword?: string;
  iosPay?: boolean;
  isBuyLimitOnce?: boolean;
  createdAt?: string;
  updatedAt?: string;
  credits?: UserCredits;
}

export interface UserCredits {
  id?: number;
  freeCoin: number; // 免费积分
  monthCoin: number; // 月度积分
  monthCoinDate?: string; // 月度到期时间
  totalCoin: number; // 总积分
  usedCoin: number; // 使用积分
  leftCoin: number; // 剩余积分
  concurrentJob: number; // 并发任务数
}
