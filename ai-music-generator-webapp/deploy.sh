#!/bin/bash

# Project directory (replace with the actual path of your project)
PROJECT_DIR="/home/<USER>/ai-music-server"

# PM2 application name (replace with your PM2 app name)
PM2_APP_NAME="ai-music-server"

# Print the current time
echo "Deploying updates at $(date)"

# Navigate to the project directory
cd $PROJECT_DIR

# Pull the latest code from the repository
echo "Pulling latest code from repository..."
git pull origin main

# Check if git pull was successful
if [ $? -ne 0 ]; then
    echo "git pull failed. Exiting script."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Check if npm install was successful
if [ $? -ne 0 ]; then
    echo "npm install failed. Exiting script."
    exit 1
fi

# Build the project
echo "Building the project..."
npm run build

# Check if npm run build was successful
if [ $? -ne 0 ]; then
    echo "npm run build failed. Exiting script."
    exit 1
fi

# Restart the PM2 process
echo "Restarting the application with PM2..."
pm2 restart $PM2_APP_NAME

# Check if pm2 restart was successful
if [ $? -ne 0 ]; then
    echo "pm2 restart failed. Exiting script."
    exit 1
fi

echo "Deployment completed successfully at $(date)"