var t = require("../../services/index");
Component({
  properties: {
    musicList: { type: Array },
    extClass: { type: String, value: "" },
  },
  data: {
    list: [],
    ids: [],
    scrollOffset: 0,
    animationData: "",
    stepPx: 1,
    someMaxOffset: 0,
    scrollInterval: 400,
  },
  lifetimes: {
    attached: function () {
      this.getNoticeList();
    },
  },
  pageLifetimes: { show: function () {} },
  methods: {
    getNoticeList: function () {
      var e = this;
      wx.getStorageSync("accessToken") &&
        t.noticeList({}).then(function (t) {
          if (t.data) {
            var i = [];
            t.data.forEach(function (t) {
              i.push(t.id);
            }),
              e.setData({ list: t.data, ids: i }, function () {
                e.getWidth();
              });
          }
        });
    },
    getWidth: function () {
      var t = this;
      setTimeout(function () {
        wx.createSelectorQuery()
          .in(t)
          .selectAll(".notice-text")
          .boundingClientRect(function (e) {
            var i;
            t.data.someMaxOffset =
              null === (i = e[0]) || void 0 === i ? void 0 : i.width;
          })
          .exec();
      }, 100);
    },
    onClose: function () {
      var e = this;
      t.noticeClose({ ids: this.data.ids }).then(function (t) {
        0 == t.code && e.setData({ list: [], ids: [] });
      });
    },
    startScroll: function () {
      var t = this,
        e = this.data,
        i = e.someMaxOffset,
        s = e.scrollInterval,
        a = e.stepPx;
      setInterval(function () {
        if (t.data.scrollOffset >= i) t.setData({ scrollOffset: 0 });
        else {
          var e = wx.createAnimation({
            duration: 4e3,
            timingFunction: "linear",
          });
          e.translateX(t.data.scrollOffset + a).step(),
            t.setData({
              animationData: e.export(),
              scrollOffset: t.data.scrollOffset + a,
            }),
            console.log(e.export());
        }
      }, s);
    },
  },
});
