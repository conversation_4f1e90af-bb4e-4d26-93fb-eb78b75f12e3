import { getDb } from "@/models/db";
import {
  findUser<PERSON>yP<PERSON>,
  findUserByUserId,
  insertUser,
  insertUserTx,
} from "@/models/user";
import {
  findUserAuthByOpenId,
  insertUserAuth,
  insertUserAuthTx,
} from "@/models/user_auth";

import { User } from "@/types/user";
import { UserAuth } from "@/types/user_auth";

export async function saveUser(user: User) {
  try {
    if (user.user_id) {
      // Ensure phone is not undefined before proceeding
      const existUser = await findUserByUserId(user.user_id);
      if (!existUser) {
        await insertUser(user);
      }
    } else {
      console.log("user.user_id is undefined.");
      // Handle the case where user.phone is undefined,
      // such as logging an error or throwing an exception.
    }
  } catch (e) {
    console.error("save user error=", e);
  }
}

export async function saveUserAndAuth(user: User, user_auth: UserAuth) {
  const db = await getDb().connect(); // 获取连接
  try {
    if (user_auth.open_id) {
      await db.query("BEGIN"); // 开始事务
      const existUserAuth = await findUserAuthByOpenId(user_auth.open_id);
      if (!existUserAuth) {
        await insertUserTx(user, db); // 确保传递db参数
        await insertUserAuthTx(user_auth, db);
      }
      await db.query("COMMIT"); // 提交事务
    } else {
      console.log("user_auth.open_id is undefined.");
    }
  } catch (e) {
    await db.query("ROLLBACK"); // 回滚事务
    throw e; // 重新抛出异常，以便调用者可以处理
  } finally {
    db.release(); // 释放连接
  }
}

export async function findUserByOpenId(
  open_id: string
): Promise<User | undefined> {
  try {
    const existUserAuth = await findUserAuthByOpenId(open_id);
    if (existUserAuth) {
      const existUser = await findUserByUserId(existUserAuth.user_id);
      if (existUser) {
        return existUser;
      }
    }
  } catch (error) {
    console.error("findUserByOpenId error=", error);
  }
}
