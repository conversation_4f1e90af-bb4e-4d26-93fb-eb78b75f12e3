var t = getApp().globalData,
  n = require('./../../utils/util.js'),
  e = require('./../../services/index'),
  i = wx.getBackgroundAudioManager();
Page({
  data: {
    alltime: 0,
    playurl: '',
    playIcon: 'icon-play',
    cdCls: 'pause',
    currentLyric: null,
    currentLineNum: 0,
    toLineNum: -1,
    currentSong: null,
    dotsArray: new Array(2),
    currentDot: 0,
    playMod: 1,
    duration: '',
    width: '',
    audioTime: 0,
    errorMsg: '',
  },
  // 分享
  onShareAppMessage: function () {
    return (
      {
        title: this.data.currentSong.title,
        path: '/pages/play/play?clipId='.concat(this.data.currentSong.music_id),
        imageUrl: this.data.currentSong.image_url,
        success: function (t) { //现在微信不让判断了
          console.log('--- 分享成功 ---', t);
        },
        fail: function () {
          console.log('--- 分享失败 ---', path);
        },
      }
    );
  },
  // 分享到朋友圈
  onShareTimeline(){
    return {
      title: this.data.currentSong.title,
      query: 'clipId=' + this.data.currentSong.music_id,
      imageUrl: this.data.currentSong.image_url,
    }
  },
  onLoad: function (t) {
    (this.clipId = t.clipId),
      wx.showShareMenu({
        withShareTicket: !0,
        menus: ['shareAppMessage', 'shareTimeline'],
      }),
      this.clipId ? this.getMusicByClipId() : this._init();
  },
  onShow: function () {},
  getMusicByClipId: function () {
    var n = this;
    // console.log('getMusicByClipId this=', this)
    e.getMusicByClipId({ id: this.clipId, from: 'mini' }).then(function (e) {
      0 == e.code &&
        ((t.currentIndex = 0),
        (t.songlist = [e.data]),
        wx.setStorageSync('songlist', [e.data]),
        n._init([e.data]));
    });
  },
  _init: function (n) {
    var e = (t.songlist.length && t.songlist) || wx.getStorageSync('songlist'),
      a = t.songlist[t.currentIndex] || (e && e[t.currentIndex]),
      r = a && a.music_metadata.duration;
    n && ((e = n), (r = (a = n[0]) && a.music_metadata.duration)),
      this.setData({
        alltime: r,
        currentSong: a,
        duration: this._formatTime(r),
        songslist: e,
        currentIndex: t.currentIndex,
      });
    var o = wx.getStorageSync('currentSongClipId');
    // if (a.clipId == o) {
    if (a.music_id == o) {
      var s = wx.getStorageSync('currentTime');
      (i.currentTime = Number(s)),
        this.createAudio(a, !0),
        this.setData({ playIcon: 'icon-pause', cdCls: 'play' });
    } else
      wx.setStorageSync('currentSongClipId', a.clipId),
        wx.showLoading({ title: '加载中...' }),
        this.getPlayUrl(a);
  },
  getPlayUrl: function (t) {
    var n = this;
    wx.request({
      // url: t.audio_url,
      url: t.oss_audio_url,
      method: 'GET',
      responseType: 'arraybuffer',
      success: function (e) {
        e.data
          ? n.createAudio(t)
          : wx.showToast({
              title: '本歌曲暂时不能播放。请切换下一首',
              icon: 'none',
            });
      },
      fail: function (t) {
        wx.showToast({ title: '网络异常，本歌曲暂时不能播放', icon: 'none' });
      },
    });
  },
  createAudio: function (t, n) {
    var e = this,
      a = this;
    n ||
      // ((i.src = t.audio_url), (i.title = t.title), (i.coverImgUrl = t.image_url)),
      ((i.src = t.oss_audio_url), (i.title = t.title), (i.coverImgUrl = t.image_url)),
      i.onCanplay(function () {
        wx.hideLoading();
      }),
      i.onPlay(function () {
        e.setData({ playIcon: 'icon-pause', cdCls: 'play' });
      }),
      i.onPause(function () {
        e.setData({ playIcon: 'icon-play', cdCls: 'pause' });
      }),
      i.onStop(function () {
        e.setData({ playIcon: 'icon-play', cdCls: 'pause' }),
          3 !== a.data.playMod || a._init();
      }),
      i.onEnded(function () {
        a.createAudio(a.data.currentSong, !1);
      }),
      i.onTimeUpdate(function () {
        var t = i.currentTime;
        e.setData({
          currentTime: e._formatTime(t),
          percent: t / e.data.currentSong.duration,
          width: (t / e.data.alltime) * 100,
        }),
          wx.setStorageSync('currentTime', t.toString());
      });
  },
  _formatTime: function (t) {
    var n = ((t |= 0) / 60) | 0,
      e = this._pad(t % 60);
    return ''.concat(n, ':').concat(e);
  },
  _pad: function (t) {
    for (
      var n =
          arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2,
        e = t.toString().length;
      e < n;

    )
      (t = '0' + t), e++;
    return t;
  },
  changeMod: function () {
    var t = this.data.playMod + 1;
    t > 3 && (t = 1), this.setData({ playMod: t });
  },
  prev: function () {
    (t.currentIndex = this.getNextIndex(!1)),
      0 !== t.currentIndex
        ? this._init()
        : this.setData({ errorMsg: '返回首页，选择更多歌曲' });
  },
  next: function () {
    (t.currentIndex = this.getNextIndex(!0)),
      0 !== t.currentIndex
        ? this._init()
        : this.setData({ errorMsg: '返回首页，选择更多歌曲' });
  },
  getNextIndex: function (e) {
    var i = t.currentIndex,
      a = this.data.playMod,
      r = this.data.songslist.length;
    return 2 === a
      ? n.randomNum(r)
      : e
        ? i + 1 == r
          ? 0
          : i + 1
        : i - 1 < 0
          ? r - 1
          : i - 1;
  },
  togglePlaying: function () {
    i.paused ? i.play() : i.pause();
  },
  openList: function () {
    this.data.songslist.length && this.setData({ translateCls: 'uptranslate' });
  },
  close: function () {
    this.setData({ translateCls: 'downtranslate' });
  },
  playthis: function (n) {
    var e = n.currentTarget.dataset.index;
    (t.currentIndex = e), this._init(), this.close();
  },
  changeDot: function (t) {
    this.setData({ currentDot: t.detail.current });
  },
  sliderChanging: function (t) {
    i.pause();
  },
  sliderChange: function (t) {
    var n = t.detail.value;
    (this.width = n),
      (n = parseInt((n * this.data.alltime) / 100)),
      setTimeout(function () {
        i.seek(n), i.play();
      }, 300);
  },
  onBack: function () {
    wx.navigateBack();
  },
});
