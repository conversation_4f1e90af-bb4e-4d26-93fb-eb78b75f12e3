{"name": "ai-music", "description": "Use API to call the music generation service of suno.ai, and easily integrate it into agents like GPTs.", "author": {"name": "kevint<PERSON>", "url": "https://github.com/Kevin-free"}, "license": "LGPL-3.0-or-later", "version": "1.3.3", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@vercel/analytics": "^1.2.2", "axios": "^1.6.8", "axios-cookiejar-support": "^5.0.1", "express": "^4.19.2", "formidable": "^3.5.1", "hexoid": "^1.0.0", "ioredis": "^3.2.2", "moment": "^2.30.1", "next": "14.1.4", "next-swagger-doc": "^0.4.0", "node-fetch": "^3.3.2", "pg": "^8.11.5", "pg-hstore": "^2.3.4", "pino": "^8.20.0", "pino-pretty": "^11.0.0", "qiniu": "^7.11.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "sequelize": "^6.37.2", "swagger-ui-react": "^5.14.0", "tough-cookie": "^4.1.3", "user-agents": "^1.1.168", "uuid": "^9.0.1", "wechatpay-node-v3": "^2.2.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.4.23"}, "devDependencies": {"@tailwindcss/typography": "^0.5.12", "@types/express": "^4.17.21", "@types/ioredis": "^3.2.26", "@types/node": "^20.12.5", "@types/pg": "^8.11.0", "@types/react": "^18.2.75", "@types/react-dom": "^18.2.24", "@types/sequelize": "^4.28.20", "@types/swagger-ui-react": "^4.18.3", "@types/tough-cookie": "^4.0.5", "@types/user-agents": "^1.0.4", "@types/uuid": "^9.0.8", "@types/xml2js": "^0.4.14", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.1.4", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.4"}}