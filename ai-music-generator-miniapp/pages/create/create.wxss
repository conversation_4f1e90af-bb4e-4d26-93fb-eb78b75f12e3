.index-page {
  box-sizing: border-box;
  color: #fff;
  font-size: 30rpx;
  min-height: 100vh;
}
.index-page .bg-image {
  position: fixed;
  top: 0;
  z-index: -1;
}
.main {
  font-size: 28rpx;
  padding-bottom: 190rpx;
}
.input-box {
  padding: 20rpx;
}
.custom-switch-box {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding-bottom: 10rpx;
  padding-top: 35rpx;
  position: relative;
}
.custom-switch {
  background-color: #b2b2b2;
  border-radius: 50rpx;
  cursor: pointer;
  display: inline-block;
  font-size: 24rpx;
  height: 39rpx;
  margin: 0 10rpx 5rpx 0rpx;
  position: relative;
  transition: background-color 0.3s;
  width: 76rpx;
}
.custom-switch-box .tips {
  background: #3b3b3b;
  border-radius: 10rpx;
  border-radius: 15rpx;
  bottom: 150rpx;
  color: #fff;
  display: none;
  font-size: 24rpx;
  left: 100rpx;
  margin: 0 0 10rpx 10rpx;
  padding: 15rpx;
  position: absolute;
  white-space: normal;
  width: 480rpx;
}
.custom-switch-box .active.tips {
  display: block;
}
.custom-switch-box .img {
  height: 36rpx;
  margin-left: 10rpx;
  width: 36rpx;
}
.custom-switch.active {
  background-color: #ff3a3a;
}
.custom-switch span {
  color: #666;
  display: inline-block;
  font-family: PingFang SC-Bold, PingFang SC;
  font-size: 14px;
  font-size: 24rpx;
  font-weight: 700;
  margin-top: 15rpx;
}
.custom_left {
  color: #fff;
  float: left;
  margin-left: 8rpx;
  margin-top: 7rpx;
}
.custom_right {
  color: #fff;
  float: right;
  margin-right: 10rpx;
  margin-top: 6rpx;
  opacity: 100%;
}
.switch-btn {
  background-color: #fff;
  border-radius: 50%;
  height: 30rpx;
  left: 5rpx;
  position: absolute;
  top: 5rpx;
  transition: background-color 0.3s, -webkit-transform 0.3s;
  transition: transform 0.3s, background-color 0.3s;
  transition: transform 0.3s, background-color 0.3s, -webkit-transform 0.3s;
  width: 30rpx;
}
.custom-switch.active .switch-btn {
  background-color: #fff;
  transform: translateX(37rpx);
}
.tips_box {
  position: relative;
}
.input-content .tabs {
  padding: 30rpx 0 20rpx;
}
.input-content .tabs,
.tabs {
  display: -webkit-flex;
  display: flex;
  gap: 30rpx;
}
.tabs {
  margin-left: 10rpx;
}
.tabs .tab {
  opacity: 0.5;
}
.tabs .tab.active {
  border-bottom: 1px solid #ff3a3a;
  border-radius: 2rpx;
  font-size: 26rpx;
  opacity: 1;
  padding-bottom: 10rpx;
}
.input-content .tabs .icon {
  margin-right: 10rpx;
  position: relative;
  top: -1px;
  width: 30rpx;
}
.input-content .textarea-box {
  /* background: rgba(29, 34, 41, 0.8); */
  /* border: 1px solid hsla(38, 93%, 77%, 0.6); */
  background: rgba(0, 0%, 100%, 0.07);
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  border-radius: 10rpx;
  box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.5);
  padding: 20rpx 20rpx 10rpx;
}
.input-content .textarea-box::after {
  clear: both;
  content: "";
  display: block;
}
.input-content .textarea {
  font-size: 28rpx;
  height: 180rpx;
  width: 100%;
}
.input-content .lyricinput {
  font-size: 28rpx;
  height: 250rpx;
  width: 100%;
}
.input-content.style .textarea {
  height: 80rpx;
}
.picker-box {
  margin: 0 0 20rpx;
}
.picker {
  background: rgba(29, 34, 41, 0.8);
  border: 1px solid hsla(38, 93%, 77%, 0.6);
  border-radius: 10rpx;
  box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.5);
  font-size: 24rpx;
  padding: 15rpx;
}
.create-btn {
  bottom: 120rpx;
  padding: 10rpx 0;
  position: fixed;
  width: 100%;
  z-index: 9;
}
.create-btn .btn {
  background: #ff3a3a;
  border-radius: 80rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin: auto;
  padding: 20rpx 0rpx;
  text-align: center;
  width: 90%;
}
.create-btn .btn:disabled {
  background: #b2b2b2;
  color: #fff;
}

.random-btn {
  background: #32373f;
  border-radius: 8rpx;
  color: #cececeeb;
  cursor: pointer;
  float: right;
  font-size: 26rpx;
  padding: 6rpx 15rpx;
}
.random-btn .loadind_img {
  animation: rotating 0.3s linear infinite;
  height: 30rpx;
  width: 30rpx;
}
.random-btn .icon {
  height: 30rpx;
  margin-right: 8rpx;
  width: 30rpx;
}
.input {
  /* background: rgba(29, 34, 41, 0.8); */
  /* border: 1px solid hsla(38, 93%, 77%, 0.6); */
  background: rgba(0, 0%, 100%, 0.07);
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  border-radius: 10rpx;
  box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.5);
  padding: 10rpx;
}
.input-box .title {
  justify-content: space-between;
  padding: 20rpx 0 10rpx;
}
.clip-box,
.input-box .title,
.input-box .title .flex {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.clip-box {
  gap: 20rpx;
}
.clipimg {
  height: 120rpx;
  width: 120rpx;
}
.clip-box .style {
  color: #cbd5e0;
  font-size: 24rpx;
  max-width: 210px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clear-btn {
  float: right;
}
.clear-btn .icon {
  height: 32rpx;
  width: 32rpx;
}
.clip_input {
  border: 1px solid #484848;
  border-radius: 8rpx;
  display: inline-block;
  line-height: 20px;
  margin-left: 20rpx;
  padding: 10rpx;
  text-align: center;
  width: 100rpx;
}
.pickerclass {
  background-color: #000;
}
.header-flex {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.header-flex .item {
  background: rgba(29, 34, 41, 0.8);
  border-radius: 10rpx;
  padding: 20rpx 0;
  text-align: center;
  width: 50%;
}
.header-flex .item.active {
  /* border: 1px solid hsla(38, 93%, 77%, 0.6); */
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.5);
  color: #ff3a3a;
}
.noshadow {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

.example {
  color: #acacac;
  font-size: 24rpx;
  margin: 20rpx;
}
.example .title {
  color: #fff;
  font-size: 30rpx;
}
.only {
  font-size: 30rpx;
}
.example .item {
  margin: 15rpx 20rpx 10rpx 0;
}
.example .text {
  /* background: linear-gradient(
      hsla(38, 93%, 77%, 0),
      hsla(38, 93%, 77%, 0.2) 150%
    ),
    #b9acac38; */
  background: #282725;
  border-radius: 10rpx;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  display: block;
  font-size: 30rpx;
  padding: 15rpx 20rpx;
}
.example .text.active {
  border: 1px solid #4075ff;
}
/* 
.style-box {
  padding-bottom: 10rpx;
  position: relative;
}

.tips_dialog .weui-dialog {
  border-radius: 10rpx;
  transform: translate(0, -55%);
  width: 90%;
}
.tips_dialog .weui-dialog__title {
  font-weight: 400;
  padding-top: 10px;
}
.tips_dialog .weui-dialog__hd {
  padding: 10rpx !important;
}
.tips_dialog .weui-dialog__bd {
  font-size: 28rpx;
  margin-bottom: 0;
} */
