import {
  downloadFileV2,
  downloadFileV3,
  uploadToQiniuV2,
  uploadToQiniuV3,
} from "@/lib/index";
import logger from "@/lib/logger";
import { updateMusicInfoV3, updateOssUrlsV2 } from "@/models/music";
import { checkUrlIsProcessed, markUrlIsProcessed } from "@/models/url_log";
import { Music } from "@/types/music";
import fs from "fs";

export const getMusicsFromFile = async (): Promise<Music[]> => {
  try {
    const dataFile = process.env.MUSICS_DATA_FILE;
    if (!dataFile) {
      return [];
    }

    const data = fs.readFileSync(dataFile, "utf8");
    const jsonData = JSON.parse(data);

    console.log(`---getMusicsFromFile jsonData=${JSON.stringify(jsonData)}`);

    let musics: Music[] = [];
    jsonData.map((item: any) => {
      musics.push({
        music_id: item.id,
        user_id: item.user_id,
        video_url: item.video_url,
        audio_url: item.audio_url,
        image_url: item.image_url,
        image_large_url: item.image_large_url,
        major_model_version: item.major_model_version,
        model_name: item.model_name,
        is_liked: item.is_liked,
        display_name: item.display_name,
        is_trashed: item.is_trashed,
        reaction: item.reaction,
        status: item.status,
        title: item.title,
        play_count: item.play_count,
        upvote_count: item.upvote_count,
        is_public: item.is_public,
        created_at: item.created_at,
        music_metadata: {
          music_id: item.id,
          user_id: item.user_id,
          tags: item.metadata.tags,
          prompt: item.metadata.prompt,
          gpt_description_prompt: item.metadata.gpt_description_prompt,
          audio_prompt_id: item.metadata.audio_prompt_id,
          history: item.metadata.history,
          concat_history: item.metadata.concat_history,
          type: item.metadata.type,
          duration: item.metadata.duration,
          refund_credits: item.metadata.refund_credits,
          stream: item.metadata.stream,
          error_type: item.metadata.error_type,
          error_message: item.metadata.error_message,
          created_at: item.created_at,
        },
      });
    });
    console.log(`---getMusicsFromFile musics=${JSON.stringify(musics)}`);

    return musics;
  } catch (error) {
    console.error("getMusicsFromFile error=", error);
    return [];
  }
};

// download and upload file to qiniu
export async function processMusicFileV2(music: Music) {
  try {
    // 定义获取文件名的辅助函数
    const getFileName = (url: string) => url.split("/").pop();

    // 根据是否有URL下载资源

    // 并行下载资源
    const [videoBuffer, audioBuffer, imageBuffer] = await Promise.all([
      music.video_url ? downloadFileV2(music.video_url) : null,
      music.audio_url ? downloadFileV2(music.audio_url) : null,
      music.image_url ? downloadFileV2(music.image_url) : null,
    ]);

    // 并行上传到七牛云，并更新music对象中的URL
    const [ossVideoUrl, ossAudioUrl, ossImageUrl] = await Promise.all([
      videoBuffer
        ? uploadToQiniuV2(videoBuffer, `dev/${getFileName(music.video_url!)}`)
        : "",
      audioBuffer
        ? uploadToQiniuV2(audioBuffer, `dev/${getFileName(music.audio_url!)}`)
        : "",
      imageBuffer
        ? uploadToQiniuV2(imageBuffer, `dev/${getFileName(music.image_url!)}`)
        : "",
    ]);
    music.oss_video_url = ossVideoUrl;
    music.oss_audio_url = ossAudioUrl;
    music.oss_image_url = ossImageUrl;
  } catch (error) {
    console.error("Error processing music file", error);
  }
}

export async function processMusicFileV3(music: Music) {
  try {
    logger.info(`---processMusicFileV3 开始处理音乐文件：${music.music_id}`);

    // 单独处理音频文件
    let ossAudioUrl = music.oss_audio_url; // 默认使用已有的URL
    if (music.audio_url && !(await checkUrlIsProcessed(music.audio_url))) {
      ossAudioUrl = await downloadAndUpload(
        music.music_id,
        music.audio_url,
        "audio"
      );
    }

    // 更新music对象中的URL
    music.oss_audio_url = ossAudioUrl;

    logger.info("音乐文件处理完成", {
      audioUrl: ossAudioUrl,
    });
  } catch (error) {
    logger.error("处理音乐文件中发生错误", error);
  }
}

// 已废弃！使用 processOssUrl
export async function processVideoAudioUrl(
  url: string,
  type: string,
  music: Music
): Promise<void> {
  try {
    const ossUrl = await downloadAndUpload(music.music_id, url, type);
    if (type === "video") {
      music.oss_video_url = ossUrl;
    } else if (type === "audio") {
      music.oss_audio_url = ossUrl;
    }
    // logger.debug(`---processed ${type} music=${JSON.stringify(music)}`);
    await updateMusicInfoV3(music);
  } catch (error) {
    logger.error(`处理 ${type} 失败: ${error}`);
    // 无需进一步操作，失败已经在 downloadAndUpload 中处理
  }
}

export async function processOssUrl(
  url: string,
  type: string,
  music: Music
): Promise<void> {
  try {
    const ossUrl = await downloadAndUpload(music.music_id, url, type);
    if (type === "video") {
      music.oss_video_url = ossUrl;
    } else if (type === "audio") {
      music.oss_audio_url = ossUrl;
    } else if (type === "image") {
      music.oss_image_url = ossUrl;
    }
    // logger.debug(`---processed ${type} music=${JSON.stringify(music)}`);
    await updateOssUrlsV2(music);
  } catch (error) {
    logger.error(`处理 ${type} 失败: ${error}`);
    // 无需进一步操作，失败已经在 downloadAndUpload 中处理
  }
}

export async function processResources(music: Music) {
  try {
    logger.info(`---processResources 开始处理音乐文件：${music.music_id}`);

    // 处理视频文件
    if (music.video_url && !(await checkUrlIsProcessed(music.video_url))) {
      music.oss_video_url = await downloadAndUpload(
        music.music_id,
        music.video_url,
        "video"
      );
    }
    // 处理音频文件
    if (music.audio_url && !(await checkUrlIsProcessed(music.audio_url))) {
      music.oss_audio_url = await downloadAndUpload(
        music.music_id,
        music.audio_url,
        "audio"
      );
    }
    // 处理图片文件
    if (music.image_url && !(await checkUrlIsProcessed(music.image_url))) {
      music.oss_image_url = await downloadAndUpload(
        music.music_id,
        music.image_url,
        "image"
      );
    }

    logger.info("音乐文件处理完成");
  } catch (error) {
    logger.error("---processResources 处理音乐文件中发生错误", error);
  }
}

// 封装下载和上传
export async function downloadAndUpload(
  musicId: string,
  url: string,
  type: string
): Promise<string> {
  await markUrlIsProcessed(url, true); // 标记URL已处理
  // logger.info(`开始下载${type}文件：${url}`);
  const buffer = await downloadFileV3(url, musicId);
  const ossUrl = await uploadToQiniuV3(buffer, getFileName(url), url, musicId);
  // logger.info(`上传${type}文件完成：${ossUrl}`);
  return ossUrl;
}

// 定义获取文件名的辅助函数
const getFileName = (url: string) => url.split("/").pop() || "";
