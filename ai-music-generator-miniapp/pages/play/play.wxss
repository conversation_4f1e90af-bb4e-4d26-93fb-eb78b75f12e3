.player .normal-player {
  background: #222;
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 150;
}
.player .head {
  height: 60px;
}
.player .head .img {
  margin: 70rpx 0 0 10px;
  width: 22px;
}
.player .normal-player .background {
  filter: blur(40rpx);
  height: 100%;
  left: 0;
  opacity: 0.6;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}
.player .normal-player .top {
  margin-bottom: 50rpx;
  position: relative;
}
.player .normal-player .top .title {
  color: #fff;
  font-size: 36rpx;
  line-height: 80rpx;
  margin: 0 auto;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 70%;
}
.player .normal-player .top .subtitle {
  color: #fff;
  font-size: 28rpx;
  line-height: 40rpx;
  text-align: center;
}
.player .normal-player .middle {
  font-size: 0;
  height: calc(100vh - 240px);
  white-space: nowrap;
  width: 100%;
}
.player .normal-player .middle .middle-l {
  display: inline-block;
  height: 0 !important;
  overflow: visible;
  padding-top: 80% !important;
  position: relative;
  vertical-align: top;
  width: 100%;
}
.player .normal-player .middle .middle-l .cd-wrapper {
  height: 90%;
  left: 15%;
  margin-top: -28%;
  position: absolute;
  top: 50%;
  width: 70%;
}
.player .normal-player .middle .middle-l .cd-wrapper .play {
  animation-play-state: running !important;
}
.player .normal-player .middle .middle-l .cd-wrapper .pause {
  animation-play-state: paused !important;
}
.player .normal-player .middle .middle-l .cd-wrapper .cd {
  animation: rotate 20s linear infinite;
  border: 10rpx solid hsla(0, 0%, 100%, 0.1);
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.player .normal-player .middle .middle-l .cd-wrapper .cd .image {
  border-radius: 50%;
  height: 100%;
  left: 0rpx;
  position: absolute;
  top: 0rpx;
  width: 100%;
}
.player .normal-player .bottom {
  bottom: 45rpx;
  position: absolute;
  width: 100%;
}
.player .normal-player .bottom .progress-wrapper {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  margin: 0rpx auto;
  padding: 0 0 20rpx;
  width: 80%;
}
.player .normal-player .bottom .progress-wrapper .time.time-l {
  text-align: left;
}
.player .normal-player .bottom .progress-wrapper .time.time-r {
  text-align: right;
}
.player .normal-player .bottom .progress-wrapper .time {
  -webkit-box-flex: 0;
  color: #fff;
  flex: 0 0 60rpx;
  font-size: 24rpx;
  line-height: 60rpx;
  width: 60rpx;
}
.player .normal-player .bottom .operators {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  text-align: center;
}
.player .normal-player .bottom .operators .i-left .img,
.player .normal-player .bottom .operators .i-right .img {
  align-items: center;
  background: hsla(0, 0%, 100%, 0.08);
  border-radius: 50%;
  cursor: pointer;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  padding: 10px;
}
.player .normal-player .bottom .operators .img {
  height: 14px;
  width: 14px;
}
.player .normal-player .bottom .operators .i-center {
  padding: 0 80rpx;
  text-align: center;
}
.player .normal-player .bottom .operators .i-center .img {
  align-items: center;
  background: #ff3a3a;
  border-radius: 50%;
  cursor: pointer;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  padding: 14px;
}
.player .normal-player .bottom .operators .menu {
  position: absolute;
  right: 10rpx;
}
.player .normal-player .top .back .icon-back {
  color: #d43c33;
  display: block;
  font-size: 44rpx;
  padding: 18rpx;
  transform: rotate(-90deg);
}
.content-wrapper {
  height: 100%;
  position: fixed;
  top: 100%;
  transition: all 0.5s;
  width: 100%;
  z-index: 150;
}
.close-list {
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%;
}
.playlist-wrapper {
  box-sizing: border-box;
  height: 660rpx;
  padding: 15rpx 30rpx;
  width: 100%;
}
.playlist-wrapper .item {
  border-bottom: 1rpx dashed hsla(0, 0%, 100%, 0.3);
  display: -webkit-flex;
  display: flex;
  height: 90rpx;
  line-height: 90rpx;
  margin-left: 30rpx;
  padding-right: 30rpx;
  position: relative;
}
.playlist-wrapper .playing,
.playlist-wrapper .playing .name,
.playlist-wrapper .playing .play_list__line,
.playlist-wrapper .playing .singer {
  color: #d43c33 !important;
}
.playlist-wrapper .item .name {
  color: hsla(0, 0%, 100%, 0.8);
  font-size: 14px;
  max-width: 350rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.playlist-wrapper .item .play_list__line {
  color: hsla(0, 0%, 100%, 0.5);
  display: block;
  margin: 0 5px;
}
.playlist-wrapper .item .singer {
  color: hsla(0, 0%, 100%, 0.8);
  font-size: 12px;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.playlist-wrapper .item .playing-img {
  height: 24rpx;
  position: absolute;
  right: 0;
  top: 32rpx;
  width: 24rpx;
}
.play-content {
  background: rgba(0, 0, 0, 0.9);
  bottom: 0;
  height: 860rpx;
  left: 0;
  position: absolute;
  right: 0;
  transition: all 0.5s;
  width: 100%;
  z-index: 200;
}
.uptranslate {
  transform: translateY(-100%) !important;
}
.downtranslate {
  transform: translateY(100%) !important;
}
.close-playlist {
  border-top: 1px solid hsla(0, 0%, 100%, 0.3);
}
.close-playlist,
.plyer-list-title {
  font-size: 16px;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  width: 100%;
}
.plyer-list-title {
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.3);
  color: #fff;
}
.player .normal-player .bottom .progress-wrapper .progress-bar-wrapper {
  -webkit-box-flex: 1;
  flex: 1;
}
@-webkit-keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}
.middle-r {
  display: inline-block;
  height: 100%;
  vertical-align: top;
  width: 100%;
}
.lyric-wrapper {
  height: 100%;
  margin: 0 auto;
  overflow: auto !important;
  text-align: center;
  width: 80%;
}
.text {
  color: hsla(0, 0%, 100%, 0.5);
  font-size: 15px;
  height: 100%;
  line-height: 25px;
  overflow-x: hidden;
  overflow-y: scroll;
  white-space: pre;
}
.current {
  color: #fff;
}
.currentLyricWrapper {
  bottom: -80rpx;
  color: #d43c33;
  font-size: 12px;
  height: 70rpx;
  line-height: 70rpx;
}
.currentLyricWrapper,
.dots-wrapper {
  position: absolute;
  text-align: center;
  width: 100%;
}
.dots-wrapper {
  bottom: 240rpx;
  height: 20rpx;
  line-height: 20rpx;
}
.dots-wrapper .dots {
  background: hsla(0, 0%, 100%, 0.5);
  border-radius: 10rpx;
  display: inline-block;
  height: 20rpx;
  margin: 0 10rpx 10rpx;
  width: 20rpx;
}
.dots-wrapper .current {
  background: hsla(0, 0%, 100%, 0.8);
  width: 40rpx;
}
.progress-bar .bar-inner {
  background: rgba(0, 0, 0, 0.3);
  height: 4px;
  position: relative;
  top: 13px;
}
.progress-bar .bar-inner .progress {
  background: #d43c33;
  height: 100%;
  position: absolute;
}
.progress-bar .bar-inner .progress-btn-wrapper {
  height: 30px;
  left: -8px;
  position: absolute;
  top: -13px;
  width: 30px;
}
.progress-bar .bar-inner .progress-btn-wrapper .progress-btn {
  background: #d43c33;
  border: 3px solid #fff;
  border-radius: 50%;
  box-sizing: border-box;
  height: 16px;
  left: 7px;
  position: relative;
  top: 7px;
  width: 16px;
}
.noshadow {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}
