import { Pool } from "pg";

let globalPool: Pool;

export function getDb() {
  if (!globalPool) {
    const connectionString = process.env.POSTGRES_URL;
    // console.log("Creating new PG pool with connection string:", connectionString);  // 日志记录数据库连接
    console.log("Creating new PG pool with connection string."); // 添加日志输出，但不直接输出连接字符串，因为可能包含敏感信息

    globalPool = new Pool({
      connectionString,
    });
    globalPool.on('error', (err) => {
      console.error('Unexpected error on idle client', err) // 日志记录数据库错误
      process.exit(-1);
    });
  }
  return globalPool;
}