<view class="weui-tabs">
    <view class="weui-tabs-bar__wrp">
        <scroll-view scrollX enhanced="{{true}}" scrollIntoView="item_{{currentView}}" scrollWithAnimation="{{animation}}" showScrollbar="{{false}}">
            <view class="weui-tabs-bar__content">
                <view bindtap="handleTabClick" class="weui-tabs-bar__item" data-index="{{index}}" id="item_{{index}}" style="background-color: {{tabBackgroundColor}}; color: {{activeTab===index?tabActiveTextColor:tabInactiveTextColor}};" wx:for="{{tabs}}" wx:key="title">
                    <view class="weui-tabs-bar__title {{tabClass}} {{activeTab===index?activeClass:''}}" style="border-bottom-color: {{activeTab===index?tabUnderlineColor:'transparent'}}">
                        <image class="img" mode="widthFix" src="{{item.icon}}" wx:if="{{item.icon}}"></image>
                        <text class="">{{item[label]}}</text>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
    <swiper bindchange="handleSwiperChange" class="{{swiperClass}}" current="{{activeTab}}" duration="{{duration}}" style="height:{{swiperHeight[activeTab]}}px">
        <swiper-item class="swiperitem" wx:for="{{tabs}}" wx:key="index">
            <scroll-view class="swiper-content" scrollY="{{true}}" style="height:{{swiperHeight[index]}}px">
                <slot name="tab-content-{{index}}"></slot>
            </scroll-view>
        </swiper-item>
    </swiper>
</view>
