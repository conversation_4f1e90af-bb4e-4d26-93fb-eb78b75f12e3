import * as qiniu from "qiniu";
import logger from "./logger";

// 确保环境变量已正确加载
if (
  !process.env.QINIU_ACCESS_KEY ||
  !process.env.QINIU_SECRET_KEY ||
  !process.env.QINIU_BUCKET ||
  !process.env.QINIU_DOMAIN
) {
  throw new Error("Qiniu configuration environment variables are required");
}

// 配置七牛云的密钥和存储空间信息
const qiniuConfig = {
  accessKey: process.env.QINIU_ACCESS_KEY, // 从环境变量获取访问密钥
  secretKey: process.env.QINIU_SECRET_KEY, // 从环境变量获取密钥
  bucket: process.env.QINIU_BUCKET, // 七牛云存储空间名
  domain: process.env.QINIU_DOMAIN, // 存储空间绑定的域名
};

// 初始化七牛云上传凭证
const config = new qiniu.conf.Config() as any; // 使用类型断言
config.zone = qiniu.zone.Zone_z2; // 设置存储区域
const mac = new qiniu.auth.digest.Mac(
  qiniuConfig.accessKey,
  qiniuConfig.secretKey
);
const options = {
  scope: qiniuConfig.bucket,
};
const putPolicy = new qiniu.rs.PutPolicy(options);
const uploadToken = putPolicy.uploadToken(mac);

// 上传文件函数
export async function uploadToQiniu(
  fileBuffer: Buffer,
  key: string
): Promise<string> {
  try {
    logger.info(`开始上传文件到七牛云：${key}`);
    const startTime = Date.now();
    const result = await new Promise<string>((resolve, reject) => {
      const formUploader = new qiniu.form_up.FormUploader(config);
      const putExtra = new qiniu.form_up.PutExtra();
      formUploader.put(
        uploadToken,
        key,
        fileBuffer,
        putExtra,
        (err, body, info) => {
          if (err) {
            logger.error(`上传文件失败：${key}`, err);
            reject(new Error(`上传文件失败：${key}`));
          } else if (info.statusCode === 200) {
            resolve(`${qiniuConfig.domain}/${body.key}`);
          } else {
            logger.error(`上传文件失败，HTTP 状态码：${info.statusCode}`);
            reject(new Error(`上传文件失败，HTTP 状态码：${info.statusCode}`));
          }
        }
      );
    });
    const endTime = Date.now();
    logger.info(`文件上传成功：${key}，耗时：${endTime - startTime}ms`);
    return result;
  } catch (error) {
    logger.error(`上传文件到七牛云失败：${key}`, error);
    throw new Error(`上传文件到七牛云失败：${key}，错误：${error}`);
  }
}
