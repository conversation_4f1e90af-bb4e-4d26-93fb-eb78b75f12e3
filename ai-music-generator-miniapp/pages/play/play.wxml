<view>
    <view class="player">
        <view class="normal-player">
            <view class="background">
                <image src="{{currentSong.image_url}}" style="width: 100%"></image>
            </view>
            <view class="top">
                <view class="title">{{currentSong.title||'no title'}}</view>
                <view class="subtitle">{{currentSong.music_metadata.tags}}</view>
            </view>
            <swiper bindchange="changeDot" class="middle">
                <swiper-item class="middle-l">
                    <view class="cd-wrapper" ref="cdWrapper">
                        <view class="cd {{cdCls}}">
                            <image alt="" class="image" src="{{currentSong.image_url}}"></image>
                        </view>
                    </view>
                </swiper-item>
                <swiper-item class="middle-r">
                    <scroll-view class="lyric-wrapper">
                        <view class="text" ref="lyricLine" wx:if="{{currentSong.music_metadata.prompt}}">{{currentSong.music_metadata.prompt}}</view>
                        <view class="text current" wx:else>暂无歌词</view>
                    </scroll-view>
                </swiper-item>
            </swiper>
            <view class="dots-wrapper">
                <view class="dots {{currentDot==index?'current':''}}" wx:for="{{dotsArray}}" wx:key="index"></view>
            </view>
            <view class="bottom">
                <view class="progress-wrapper">
                    <text class="time time-l">{{currentTime}}</text>
                    <view class="progress-bar-wrapper">
                        <view class="progress-bar" id="progressBar">
                            <slider activeColor="#ff3a3a" backgroundColor="rgba(0,0,0,0.3)" bindchange="sliderChange" bindchanging="sliderChanging" blockSize="12" value="{{width}}"></slider>
                        </view>
                    </view>
                    <text class="time time-r">{{duration}}</text>
                </view>
                <view class="operators">
                    <view class="icon i-left noshadow">
                        <image bindtap="prev" class="img" mode="widthFix" src="./../../image/icon06.svg"></image>
                    </view>
                    <view class="icon i-center noshadow">
                        <image bindtap="togglePlaying" class="img" mode="widthFix" src="./../../image/icon07.svg" wx:if="{{playIcon=='icon-play'}}"></image>
                        <image bindtap="togglePlaying" class="img" mode="widthFix" src="./../../image/icon09.svg" wx:else></image>
                    </view>
                    <view class="icon i-right noshadow">
                        <image bindtap="next" class="img" mode="widthFix" src="./../../image/icon08.svg"></image>
                    </view>
                </view>
            </view>
        </view>
        <view class="content-wrapper {{translateCls}}">
            <view bindtap="close" class="close-list"></view>
            <view class="play-content">
                <view class="plyer-list-title">播放队列({{songslist.length}}首)</view>
                <scroll-view scrollY class="playlist-wrapper" scrollIntoView="list{{currentIndex}}">
                    <view bindtap="playthis" class="item {{index==currentIndex?'playing':''}}" data-index="{{index}}" id="list{{index}}" wx:for="{{songslist}}" wx:key="index">
                        <view class="name">{{item.title}}</view>
                        <view class="play_list__line">-</view>
                        <view class="singer">{{item.music_metadata.tags}}</view>
                        <image class="playing-img" src="./playing.gif" wx:if="{{index==currentIndex}}"></image>
                    </view>
                </scroll-view>
                <view bindtap="close" class="close-playlist">关闭</view>
            </view>
        </view>
    </view>
</view>
<mp-toptips msg="{{errorMsg}}" show="{{errorMsg}}" type="error"></mp-toptips>
