import { QueryResult, QueryResultRow } from "pg";

import { Music } from "@/types/music";
import { getDb } from "@/models/db";
import logger from "@/lib/logger";

export async function insertMusic(music: Music) {
  const db = getDb();
  try {
    await db.query("BEGIN");

    const musicRes = await (db.query as any)(
      "INSERT INTO musics (music_id, user_id, video_url, audio_url, image_url, image_large_url, major_model_version, model_name, is_liked, display_name, is_trashed, reaction, status, title, play_count, upvote_count, is_public, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18) RETURNING id",
      [
        music.music_id,
        music.user_id,
        music.video_url,
        music.audio_url,
        music.image_url,
        music.image_large_url,
        music.major_model_version,
        music.model_name,
        music.is_liked,
        music.display_name,
        music.is_trashed,
        music.reaction,
        music.status,
        music.title,
        music.play_count,
        music.upvote_count,
        music.is_public,
        music.created_at,
      ]
    );

    await (db.query as any)(
      "INSERT INTO music_metadatas (music_id, user_id, tags, prompt, gpt_description_prompt, audio_prompt_id, history, concat_history, type, duration, refund_credits, stream, error_type, error_message, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)",
      [
        music.music_metadata.music_id,
        music.music_metadata.user_id,
        music.music_metadata.tags,
        music.music_metadata.prompt,
        music.music_metadata.gpt_description_prompt,
        music.music_metadata.audio_prompt_id,
        music.music_metadata.history,
        JSON.stringify(music.music_metadata.concat_history),
        music.music_metadata.type,
        music.music_metadata.duration,
        music.music_metadata.refund_credits,
        music.music_metadata.stream,
        music.music_metadata.error_type,
        music.music_metadata.error_message,
        music.music_metadata.created_at,
      ]
    );

    await db.query("COMMIT");
  } catch (e) {
    await db.query("ROLLBACK");
    throw e;
  }
}

export async function getUserMusicsCount(user_id: string): Promise<number> {
  const db = getDb();
  const res = await db.query(
    `SELECT count(1) as count FROM musics WHERE user_id = $1 AND status = 'complete'`,
    [user_id]
  );
  if (res.rowCount === 0) {
    return 0;
  }

  const { rows } = res;
  const row = rows[0];

  return row.count;
}

export async function getMusicIds(): Promise<string[]> {
  const db = getDb();
  const res = await db.query(`SELECT music_id FROM musics`);
  if (res.rowCount === 0) {
    return [];
  }

  const { rows } = res;
  let uuids: string[] = [];
  rows.forEach((row) => {
    uuids.push(row.uuid);
  });

  return uuids;
}

// Function to get music ids that already have all OSS URLs
export async function getMusicIdsExistAllOss(): Promise<string[]> {
  const db = getDb();
  const query = `
      SELECT music_id FROM musics
      WHERE oss_video_url IS NOT NULL AND oss_audio_url IS NOT NULL AND oss_image_url IS NOT NULL;
  `;
  const res = await db.query(query);
  if (res.rowCount === 0) {
    return [];
  }
  return res.rows.map((row) => row.music_id);
}

export async function getMusicsIsRetry(): Promise<Music[]> {
  const db = getDb();
  const query = `SELECT m.*, mm.* FROM musics m
  JOIN music_metadatas mm ON m.music_id = mm.music_id
  WHERE m.is_retry = $1
  ORDER BY m.id ASC`;
  const params = [true];
  try {
    const res = await db.query(query, params); // 执行查询操作
    if (res.rowCount === 0) {
      return [];
    }
    const musics = getMusicsFromSqlResult(res);
    return musics;
  } catch (error) {
    logger.error("getMusicsIsRetry error=", { query, params, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`getMusicsIsRetry error=: ${error}`); // 抛出错误，以供上层处理
  }
}

export async function getMusics(
  from: string,
  pageSize: number,
  rankCategory: string
): Promise<Music[]> {
  if (from == "mini") {
    pageSize = 20;
  }
  if (pageSize <= 0) {
    pageSize = 50;
  }

  const db = getDb();
  let query = "";
  const params = [pageSize];

  // 根据rankCategory选择不同的排序和过滤逻辑
  switch (rankCategory) {
    case "10":
      // 今日推荐：随机顺序
      query = `SELECT m.*, mm.*
               FROM musics m
               JOIN music_metadatas mm ON m.music_id = mm.music_id
               WHERE m.status = 'complete' AND m.is_trashed = false AND m.is_public = true AND m.oss_audio_url <> ''
               ORDER BY RANDOM()
               LIMIT $1`;
      break;
    case "20":
      // 热歌榜：根据播放次数和点赞次数总和降序
      query = `SELECT m.*, mm.*
               FROM musics m
               JOIN music_metadatas mm ON m.music_id = mm.music_id
               WHERE m.status = 'complete' AND m.is_trashed = false AND m.is_public = true AND m.oss_audio_url <> ''
               ORDER BY (m.play_count + m.upvote_count) DESC
               LIMIT $1`;
      break;
    case "30":
      // 新歌榜：根据创建时间降序
      query = `SELECT m.*, mm.*
               FROM musics m
               JOIN music_metadatas mm ON m.music_id = mm.music_id
               WHERE m.status = 'complete' AND m.is_trashed = false AND m.is_public = true AND m.oss_audio_url <> ''
               ORDER BY m.created_at DESC
               LIMIT $1`;
      break;
    default:
      // 默认情况：按照ID升序
      query = `SELECT m.*, mm.*
               FROM musics m
               JOIN music_metadatas mm ON m.music_id = mm.music_id
               WHERE m.status = 'complete' AND m.is_trashed = false AND m.is_public = true AND m.oss_audio_url <> ''
               ORDER BY m.id ASC
               LIMIT $1`;
      break;
  }

  // 执行查询
  const res = await db.query(query, params);
  if (res.rowCount === 0) {
    return [];
  }

  // 处理SQL查询结果
  const musics = getMusicsFromSqlResult(res);
  return musics;
}

export async function getMusicByMusicId(
  music_id: number,
  from: string
): Promise<Music | undefined> {
  const db = getDb(); // 获取数据库连接
  const query = `SELECT m.*, mm.* FROM musics m
    JOIN music_metadatas mm ON m.music_id = mm.music_id
    WHERE m.music_id = $1 LIMIT 1`;
  const params = [music_id] as any; // SQL 参数

  try {
    logger.info("Executing SQL", { query, params }); // 日志记录执行的SQL和参数
    const res = await db.query(query, params); // 执行查询操作
    logger.info("SQL Query Result", { rows: res.rows }); // 日志记录查询结果
    if (res.rowCount === 0) {
      return undefined;
    }
    const musics = getMusicsFromSqlResult(res);
    if (musics.length <= 0) {
      return undefined;
    }
    return musics[0];
  } catch (error) {
    logger.error("getMusicByMusicId error=", { query, params, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`getMusicByMusicId error=: ${error}`); // 抛出错误，以供上层处理
  }
}

export async function getMusicsByUserId(
  user_id: string,
  from: string,
  page: number,
  pageSize: number
): Promise<Music[]> {
  if (page < 1) {
    page = 1;
  }

  const db = getDb();
  const offset = (page - 1) * pageSize; // 计算分页偏移量

  // 编写SQL查询语句，包括分页逻辑
  const query = `
    SELECT m.*, mm.* FROM musics m
    JOIN music_metadatas mm ON m.music_id = mm.music_id
    WHERE m.user_id = $1
    AND m.status = 'complete'
    ORDER BY m.created_at DESC
    LIMIT $2 OFFSET $3;
  `;
  const params: any[] = [user_id, pageSize, offset];

  const res = await db.query(query, params);

  if (res.rowCount === 0) {
    return [];
  }

  const musics = getMusicsFromSqlResult(res);

  return musics;
}

// 已废弃！使用 updateMusicInfoV3
export async function updateMusicInfo(audioInfo: any): Promise<void> {
  const db = getDb();
  try {
    await db.query("BEGIN");

    // Update musics table
    let query = `
    UPDATE musics
    SET
      title = $1,
      image_url = $2,
      audio_url = $3,
      video_url = $4,
      created_at = $5,
      model_name = $6,
      status = $7
    WHERE music_id = $8;
  `;
    let params = [
      audioInfo.title,
      audioInfo.image_url,
      audioInfo.audio_url,
      audioInfo.video_url,
      audioInfo.created_at,
      audioInfo.model_name,
      audioInfo.status,
      audioInfo.id,
    ];

    await db.query(query, params);

    // Update music_metadatas table
    query = `
      UPDATE music_metadatas
      SET
        prompt = $1,
        duration = $2,
        tags = $3
      WHERE music_id = $4;
    `;
    params = [
      audioInfo.lyric,
      audioInfo.duration,
      audioInfo.tags,
      audioInfo.id,
    ];
    await db.query(query, params);

    await db.query("COMMIT");
  } catch (e) {
    await db.query("ROLLBACK");
    throw e;
  }
}

// 已废弃！使用 updateMusicInfoV3
export async function updateMusicInfoV2(music: any): Promise<void> {
  console.log(`---updateMusicInfoV2 music=${JSON.stringify(music)}`);
  const db = getDb();
  try {
    await db.query("BEGIN");

    // Update musics table
    let query = `
    UPDATE musics
    SET
      title = $1,
      image_url = $2,
      audio_url = $3,
      video_url = $4,
      oss_image_url = $5,
      oss_audio_url = $6,
      oss_video_url = $7,
      created_at = $8,
      model_name = $9,
      status = $10
    WHERE music_id = $11;
  `;
    let params = [
      music.title,
      music.image_url,
      music.audio_url,
      music.video_url,
      music.oss_image_url,
      music.oss_audio_url,
      music.oss_video_url,
      music.created_at,
      music.model_name,
      music.status,
      music.music_id,
    ];

    await db.query(query, params);

    // Update music_metadatas table
    query = `
      UPDATE music_metadatas
      SET
        prompt = $1,
        duration = $2,
        tags = $3
      WHERE music_id = $4;
    `;
    params = [
      music.music_metadata.prompt,
      music.music_metadata.duration,
      music.music_metadata.tags,
      music.music_id,
    ];
    await db.query(query, params);

    await db.query("COMMIT");
  } catch (e) {
    await db.query("ROLLBACK");
    throw e;
  }
}

export async function updateMusicInfoV3(music: any): Promise<void> {
  // logger.info(`---updateMusicInfoV3 music=${JSON.stringify(music)}`);
  const db = getDb();
  try {
    await db.query("BEGIN");

    // Dynamically build query based on non-null fields in the music object
    let fieldsToUpdate: string[] = [];
    let params = [];
    let paramCount = 1;

    [
      "title",
      "image_url",
      "audio_url",
      "video_url",
      "oss_image_url",
      "oss_audio_url",
      "oss_video_url",
      "created_at",
      "model_name",
      "status",
    ].forEach((field) => {
      if (music[field] !== null && music[field] !== undefined) {
        fieldsToUpdate.push(`${field} = $${paramCount}`);
        params.push(music[field]);
        paramCount++;
      }
    });

    if (fieldsToUpdate.length > 0) {
      let updateQuery = `UPDATE musics SET ${fieldsToUpdate.join(
        ", "
      )} WHERE music_id = $${paramCount}`;
      params.push(music.music_id);
      await db.query(updateQuery, params);
    }

    // Update music_metadatas table similarly
    fieldsToUpdate = [];
    params = [];
    paramCount = 1;

    ["prompt", "duration", "tags"].forEach((field) => {
      if (
        music.music_metadata &&
        music.music_metadata[field] !== null &&
        music.music_metadata[field] !== undefined
      ) {
        fieldsToUpdate.push(`${field} = $${paramCount}`);
        params.push(music.music_metadata[field]);
        paramCount++;
      }
    });

    if (fieldsToUpdate.length > 0) {
      let metadataUpdateQuery = `UPDATE music_metadatas SET ${fieldsToUpdate.join(
        ", "
      )} WHERE music_id = $${paramCount}`;
      params.push(music.music_id);
      await db.query(metadataUpdateQuery, params);
    }

    await db.query("COMMIT");
  } catch (e) {
    await db.query("ROLLBACK");
    logger.error("Failed to update music info", e);
    throw e;
  }
}

// 已废弃！使用 updateOssUrlsV2
export async function updateOssUrls(
  music_id: string,
  oss_video_url: string,
  oss_audio_url: string,
  oss_image_url: string
): Promise<void> {
  const db = getDb();

  try {
    const query = `
      UPDATE musics
      SET
        oss_video_url = $1,
        oss_audio_url = $2,
        oss_image_url = $3
      WHERE music_id = $4;
    `;
    const params = [oss_video_url, oss_audio_url, oss_image_url, music_id];
    await db.query(query, params);
  } catch (error) {
    console.error("Error updating OSS URLs:", error);
    throw error; // Re-throw to handle elsewhere if needed
  }
}

// Function to update OSS URLs in the database
export async function updateOssUrlsV2(music: Music) {
  const db = getDb();
  // 使用`COALESCE`函数确保只更新非空的字段值，避免覆盖已有的有效数据。
  // 同时更新`updated_at`字段为当前时间
  const query = `
      UPDATE musics SET
          oss_video_url = COALESCE($1, oss_video_url),
          oss_audio_url = COALESCE($2, oss_audio_url),
          oss_image_url = COALESCE($3, oss_image_url),
          updated_at = NOW()
      WHERE music_id = $4;
  `;
  const params = [
    music.oss_video_url,
    music.oss_audio_url,
    music.oss_image_url,
    music.music_id,
  ] as any;
  await db.query(query, params);
  // logger.info(`Updated OSS URLs for music_id=${music.music_id}`);
}

export async function updateMusicStatus(
  musicId: string,
  status: string
): Promise<void> {
  const db = getDb(); // 获取数据库连接
  const query = `UPDATE musics SET status = $1 WHERE music_id = $2;`; // SQL 更新语句
  try {
    await db.query(query, [status, musicId]); // 执行更新操作
  } catch (error) {
    console.error("Failed to update music status:", error); // 错误处理
  }
}

// 更新是否需要重试：下载和上传更新oss_url
export async function updateMusicIsRetry(
  musicId: string,
  isRetry: boolean
): Promise<void> {
  const db = getDb(); // 获取数据库连接
  const query = `UPDATE musics SET is_retry = $1 WHERE music_id = $2;`; // SQL 更新语句
  try {
    await db.query(query, [isRetry, musicId]); // 执行更新操作
  } catch (error) {
    console.error("Failed to update music status:", error); // 错误处理
  }
}

export function getMusicsFromSqlResult(
  res: QueryResult<QueryResultRow>
): Music[] {
  if (!res.rowCount || res.rowCount === 0) {
    return [];
  }

  const musics: Music[] = [];
  const { rows } = res;
  rows.forEach((row) => {
    const music = formatMusic(row);
    if (music) {
      musics.push(music);
    }
  });

  return musics;
}

export function formatMusic(row: QueryResultRow): Music | undefined {
  let music: Music = {
    music_id: row.music_id,
    user_id: row.user_id,
    video_url: row.video_url,
    audio_url: row.audio_url,
    image_url: row.image_url,
    oss_video_url: row.oss_video_url,
    oss_audio_url: row.oss_audio_url,
    oss_image_url: row.oss_image_url,
    image_large_url: row.image_large_url,
    major_model_version: row.major_model_version,
    model_name: row.model_name,
    is_liked: row.is_liked,
    display_name: row.display_name,
    is_trashed: row.is_trashed,
    reaction: row.reaction,
    status: row.status,
    title: row.title,
    play_count: row.play_count,
    upvote_count: row.upvote_count,
    is_public: row.is_public,
    is_retry: row.is_retry,
    created_at: row.created_at,
    music_metadata: {
      music_id: row.music_id,
      user_id: row.user_id,
      tags: row.tags,
      prompt: row.prompt,
      gpt_description_prompt: row.gpt_description_prompt,
      audio_prompt_id: row.audio_prompt_id,
      history: row.history,
      concat_history: row.concat_history,
      type: row.type,
      duration: row.duration,
      refund_credits: row.refund_credits,
      stream: row.stream,
      error_type: row.error_type,
      error_message: row.error_message,
      created_at: row.created_at,
    },
  };

  return music;
}
