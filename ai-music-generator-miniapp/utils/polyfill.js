var e = require('../@babel/runtime/helpers/typeof');
!(function (t) {
  if (
    'object' === ('undefined' == typeof exports ? 'undefined' : e(exports)) &&
    null != exports &&
    'number' != typeof exports.nodeType
  )
    module.exports = t();
  else if ('function' == typeof define && null != define.amd) define([], t);
  else {
    var o = t(),
      r = 'undefined' != typeof self ? self : $.global;
    'function' != typeof r.btoa && (r.btoa = o.btoa),
      'function' != typeof r.atob && (r.atob = o.atob);
  }
})(function () {
  var e = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  function t(e) {
    this.message = e;
  }
  return (
    (t.prototype = new Error()),
    (t.prototype.name = 'InvalidCharacterError'),
    {
      btoa: function (o) {
        for (
          var r, n, a = String(o), f = 0, i = e, u = '';
          a.charAt(0 | f) || ((i = '='), f % 1);
          u += i.charAt(63 & (r >> (8 - (f % 1) * 8)))
        ) {
          if ((n = a.charCodeAt((f += 3 / 4))) > 255)
            throw new t(
              "'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.",
            );
          r = (r << 8) | n;
        }
        return u;
      },
      atob: function (t) {
        for (
          var o, r, n = String(t).replace(/[=]+$/, ''), a = 0, f = 0, i = '';
          (r = n.charAt(f++));
          ~r && ((o = a % 4 ? 64 * o + r : r), a++ % 4)
            ? (i += String.fromCharCode(255 & (o >> ((-2 * a) & 6))))
            : 0
        )
          r = e.indexOf(r);
        return i;
      },
    }
  );
});
