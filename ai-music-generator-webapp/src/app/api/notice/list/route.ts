import logger from "@/lib/logger";
import { respData, respErr } from "@/lib/resp";

export async function POST(req: Request) {
  try {
    // const user_id = req.headers.get("x-user-id") || "";
    // if (!user_id) {
    //   return respErr("user_id is required");
    // }

    const notices = await getNotices(); // 调用服务层获取通知列表数据

    // 生成符合前端要求的数据结构
    const responseData = notices.map((notice) => ({
      id: notice.id,
      title: notice.title,
      content: notice.content,
      state: notice.state,
      updationDate: notice.updatedAt.getTime(),
      creationDate: notice.createdAt.getTime(),
      createdBy: notice.createdBy,
      updatedBy: notice.updatedBy,
      enabledFlag: notice.enabledFlag,
    }));

    logger.info(`noticeList rsp=${responseData}`);

    return respData(responseData); // 发送处理后的响应数据
    // return respData(null); // 发送空数据
  } catch (error) {
    console.error("noticeList error=", error);
    return respErr("noticeList error");
  }
}

/**
 * 模拟从数据库或其他源获取通知数据
 */
async function getNotices() {
  // 这里使用静态数据模拟数据库调用结果
  return [
    {
      id: 17,
      title: "618",
      content:
        '<span  style="color:#ff3a3a;">🎉 端午大放「粽」！疯狂大放「价」！音缘AI音乐限时特惠！所有充值低至5折！活动截止6月20日，届时将恢复原价格。<a href="https://oss.suno.ti-mi.cn/ti-mi.cn/music/huodong/huodong.png" target="_blank">>>查看活动海报</a></span>',
      state: 10,
      createdAt: new Date(1713957385000),
      updatedAt: new Date(1713957385000),
      startTime: new Date(1713957385000),
      endTime: new Date(1713957385000),
      createdBy: "system",
      updatedBy: null,
      enabledFlag: 1,
    },
  ];
}
