var a = getApp();

Component({
    properties: {
        navbarData: {
            type: Object,
            value: {},
            observer: function(a, t) {}
        }
    },
    data: {
        navbarData: {
            navidx: 0
        }
    },
    attached: function() {
        this.setData({
            share: a.globalData.share
        }), this.setData({
            barHeight: a.globalData.barHeight,
            barHeightrpx: a.globalData.barHeightrpx,
            lWidth: a.globalData.winWidth - 98
        });
    },
    methods: {
        switchnav: function(a) {
            var t = a.currentTarget.dataset.t;
            console.log("---nav t:", t), console.log("---data.nav :", this.data.navbarData.navidx), 
            0 == t && (console.log("---to yy"), this.setData({
                "navbarData.navidx": t
            }), wx.redirectTo({
                url: "../home/<USER>"
            })), 1 == t && (console.log("---to my"), this.setData({
                "navbarData.navidx": t
            }), wx.redirectTo({
                url: "../me/index"
            }));
        },
        _navback: function() {
            wx.navigateBack();
        },
        _backhome: function() {
            wx.redirectTo({
                url: "/pages/home/<USER>"
            });
        }
    }
});