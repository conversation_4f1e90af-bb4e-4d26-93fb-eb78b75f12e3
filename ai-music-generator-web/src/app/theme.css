:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1884 0.0128 248.5103);
  --card: oklch(0.9784 0.0011 197.1387);
  --card-foreground: oklch(0.1884 0.0128 248.5103);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1884 0.0128 248.5103);
  --primary: oklch(0.637 0.237 25.331);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.1884 0.0128 248.5103);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.9222 0.0013 286.3737);
  --muted-foreground: oklch(0.1884 0.0128 248.5103);
  --accent: oklch(0.9392 0.0166 250.8453);
  --accent-foreground: oklch(0.637 0.237 25.331);
  --destructive: oklch(0.6188 0.2376 25.7658);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9317 0.0118 231.6594);
  --input: oklch(0.9809 0.0025 228.7836);
  --ring: oklch(0.637 0.237 25.331);
  --chart-1: oklch(0.637 0.237 25.331);
  --chart-2: oklch(0.6907 0.1554 160.3454);
  --chart-3: oklch(0.8214 0.16 82.5337);
  --chart-4: oklch(0.7064 0.1822 151.7125);
  --chart-5: oklch(0.5919 0.2186 10.5826);
  --sidebar: oklch(0.9784 0.0011 197.1387);
  --sidebar-foreground: oklch(0.1884 0.0128 248.5103);
  --sidebar-primary: oklch(0.637 0.237 25.331);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9392 0.0166 250.8453);
  --sidebar-accent-foreground: oklch(0.637 0.237 25.331);
  --sidebar-border: oklch(0.9271 0.0101 238.5177);
  --sidebar-ring: oklch(0.637 0.237 25.331);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-sm:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-md:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-lg:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xl:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0 0 0);
  --foreground: oklch(0.9328 0.0025 228.7857);
  --card: oklch(0.2097 0.008 274.5332);
  --card-foreground: oklch(0.8853 0 0);
  --popover: oklch(0 0 0);
  --popover-foreground: oklch(0.9328 0.0025 228.7857);
  --primary: oklch(0.637 0.237 25.331);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9622 0.0035 219.5331);
  --secondary-foreground: oklch(0.1884 0.0128 248.5103);
  --muted: oklch(0.209 0 0);
  --muted-foreground: oklch(0.5637 0.0078 247.9662);
  --accent: oklch(0.1928 0.0331 242.5459);
  --accent-foreground: oklch(0.637 0.237 25.331);
  --destructive: oklch(0.6188 0.2376 25.7658);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.2674 0.0047 248.0045);
  --input: oklch(0.302 0.0288 244.8244);
  --ring: oklch(0.637 0.237 25.331);
  --chart-1: oklch(0.637 0.237 25.331);
  --chart-2: oklch(0.6907 0.1554 160.3454);
  --chart-3: oklch(0.8214 0.16 82.5337);
  --chart-4: oklch(0.7064 0.1822 151.7125);
  --chart-5: oklch(0.5919 0.2186 10.5826);
  --sidebar: oklch(0.2097 0.008 274.5332);
  --sidebar-foreground: oklch(0.8853 0 0);
  --sidebar-primary: oklch(0.637 0.237 25.331);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.1928 0.0331 242.5459);
  --sidebar-accent-foreground: oklch(0.637 0.237 25.331);
  --sidebar-border: oklch(0.3795 0.022 240.5943);
  --sidebar-ring: oklch(0.637 0.237 25.331);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-sm:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-md:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-lg:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xl:
    0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
