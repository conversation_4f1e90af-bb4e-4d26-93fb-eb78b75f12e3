var t = getApp().globalData,
  e = require("../../services/index");
wx.getBackgroundAudioManager();
Component({
  properties: {
    musicList: { type: Array },
    extClass: { type: String, value: "" },
  },
  data: { list: [], songDetail: {}, isPlay: !1, playTime: "" },
  observers: {
    musicList: function (t) {
      console.log(t), this.setData({ list: t });
    },
  },
  lifetimes: { attached: function () {} },
  pageLifetimes: { show: function () {} },
  methods: {
    selectItem: function (e) {
      (t.currentIndex = e.currentTarget.dataset.index),
        (t.songlist = this.data.list),
        wx.setStorageSync("songlist", this.data.list),
        wx.navigateTo({ url: "/pages/play/play" });
    },
    onLike: function (t) {
      var i = this;
      if (wx.getStorageSync("accessToken")) {
        var a = t.currentTarget.dataset.id;
        e.viewLikes({ exploreId: a, type: 20 }).then(function (e) {
          if (0 == e.code) {
            var a = t.currentTarget.dataset.index,
              s = i.data.list;
            s[a].isLike || ((s[a].isLike = !0), (s[a].likes = s[a].likes + 1)),
              i.setData({ list: s });
          }
        });
      }
    },
    onCollect: function (t) {
      var i = this;
      if (!wx.getStorageSync("accessToken"))
        return wx.navigateTo({ url: "/pages/login/login" }), !1;
      var a = t.currentTarget.dataset,
        s = a.item,
        n = a.index,
        r = this.data.list;
      e.clickCollect({ exploreId: s.id, id: s.id }).then(function (t) {
        0 == t.code && ((r[n].isCollect = !0), i.setData({ list: r }));
      });
    },
    openMore: function (t) {
      this.triggerEvent("myCustomEvent", t.currentTarget.dataset.item, {});
    },
  },
});
