Component({
  data: {
    isShowTabBar: !0,
    selected: 0,
    color: '#78797b',
    selectedColor: '#fff',
    active: !1,
    list: [
      {
        pagePath: '/pages/discover/discover',
        iconPath: '/image/tab1-discover.svg',
        selectedIconPath: '/image/tab1-discover-active.svg',
        text: '发现',
      },
      {
        pagePath: '/pages/create/create',
        iconPath: '/image/tab2-music.svg',
        selectedIconPath: '/image/tab2-music-active.svg',
        text: '创作',
        bulge: !1,
      },
      {
        pagePath: '/pages/user/user',
        iconPath: '/image/tab3-user.svg',
        selectedIconPath: '/image/tab3-user-active.svg',
        text: '我的',
      },
    ],
    animationData: {},
  },
  lifetimes: { attached: function () {} },
  pageLifetimes: {
    show: function () {},
    hide: function () {},
    resize: function () {},
  },
  methods: {
    switchTab: function (t) {
      var e = t.currentTarget.dataset.path;
      this.setData({ active: !1 }), wx.switchTab({ url: e });
    },
    onClick: function (t) {
      var e = t.currentTarget.dataset,
        a = e.path,
        i = getApp();
      wx.setStorageSync('type', e.type),
        (i.globalData = { type: e.type }),
        wx.switchTab({ url: a }),
        this.setData({ active: !1 });
    },
  },
});
