require('../../@babel/runtime/helpers/Arrayincludes');
var e = require('../../services/index'),
  t = '';
Page({
  data: {
    errorMsg: '',
    internal: null,
    isCodeLogin: !1,
    disabled: !0,
    countdown: !0,
    count: 60,
    phoneEmail: '',
    phoneEmailCode: '',
    isChecked: 1,
  },
  onLoad: function () {
    wx.hideShareMenu({});
  },
  changeLogin: function () {
    this.setData({ isCodeLogin: !0 });
  },
  changeLogin2: function () {
    this.setData({ isCodeLogin: !1 });
  },
  bindgetphonenumber: function (e) {
    'getPhoneNumber:ok' === e.detail.errMsg && this.onLogin(e);
  },
  onAgreeChange: function (e) {
    this.setData({ isChecked: e.detail.value.length > 0 });
  },
  onCheck: function () {
    wx.showToast({
      title: '请阅读同意用户协议与隐私政策',
      icon: 'none',
      duration: 2e3,
    });
  },
  onFastLogin: function(t) {
    var that = this;
    // 阻止重复登录尝试
    if (this.data.isLoggingIn) return;
    this.setData({ isLoggingIn: true });
    wx.showLoading({ title: '登录中...' }); // 显示加载提示框
    wx.login({
      success: function (a) {
        e.fastLogin({
          code: a.code,
        }).then(function(e) {
          wx.hideLoading(); // 成功后隐藏加载框
          if (0 === e.code) {
            wx.setStorageSync('accessToken', e.data.accessToken);
            wx.setStorageSync('userId', e.data.userId);
            wx.navigateBack();
          } else {
            wx.showToast({ title: '登录失败: ' + e.msg, icon: 'none' });
          }
          that.setData({ isLoggingIn: false }); // 重置登录状态
        }).catch(function(error) {
          wx.hideLoading(); // 出错也要确保隐藏加载提示框
          wx.showToast({ title: '登录请求异常', icon: 'none' });
          that.setData({ isLoggingIn: false }); // 重置登录状态
        });
      },
      fail: function () {
        wx.hideLoading(); // 登录接口调用失败也需要隐藏加载框
        wx.showToast({ title: '微信登录失败', icon: 'none' });
        that.setData({ isLoggingIn: false }); // 重置登录状态
      }
    });
  },
  onLogin: function (t) {
    wx.login({
      success: function (a) {
        e.miniLogin({
          code: a.code,
          phoneCode: t.detail.code,
          comeFrom: 1010,
        }).then(function (e) {
          0 === e.code &&
            (wx.setStorageSync('accessToken', e.data.accessToken),
            wx.setStorageSync('userId', e.data.userId),
            wx.navigateBack());
        });
      },
    });
  },
  bindPhoneInput: function (e) {
    this.setData({ phoneEmail: e.detail.value }),
      e.detail.value && this.data.phoneEmailCode
        ? this.setData({ disabled: !1 })
        : this.setData({ disabled: !0 });
  },
  bindCodeInput: function (e) {
    this.setData({ phoneEmailCode: e.detail.value }),
      e.detail.value && this.data.phoneEmail
        ? this.setData({ disabled: !1 })
        : this.setData({ disabled: !0 });
  },
  getCode: function () {
    var t = this,
      a = this.data.phoneEmail;
    if (!a) return this.setData({ errorMsg: '请输入手机号或邮箱！' }), !1;
    var n = '',
      o = '',
      i = '';
    a.includes('@')
      ? ((n = a), (o = ''), (i = '邮箱'))
      : ((o = a), (n = ''), (i = '手机')),
      this.onCountDown(),
      e.getVerifyCode({ email: n, phone: o }).then(function (e) {
        0 == e.code
          ? (t.onCountDown(),
            t.setData({
              successMsg: '验证码已发送至您的'.concat(i, '，请注意查收。'),
            }))
          : t.setData({ errorMsg: e.msg });
      });
  },
  onCountDown: function () {
    var e = this,
      a = this.data.countdown;
    a = !a;
    var n = 60;
    t ||
      (this.setData({ countdown: !1 }),
      (t = setInterval(function () {
        n > 0 && n <= 60
          ? (n--, e.setData({ count: n }))
          : (e.setData({ countdown: !0, count: 60 }),
            clearInterval(t),
            (t = null));
      }, 1e3)));
  },
  onPhoneLogin: function () {
    var t = this,
      a = this.data,
      n = a.phoneEmail,
      o = a.phoneEmailCode;
    if (!n) return this.setData({ errorMsg: '请输入手机号' }), !1;
    if (!o) return this.setData({ errorMsg: '请输入验证码' }), !1;
    if (!this.data.isChecked)
      return this.setData({ errorMsg: '请阅读并同意用户协议与隐私政策' }), !1;
    var i = '',
      s = '';
    n.includes('@') ? ((i = n), (s = '')) : ((s = n), (i = '')),
      wx.login({
        success: function (t) {
          var a = this,
            n = {
              email: i,
              phone: s,
              code: o,
              comeFrom: 1010,
              miniCode: t.code,
            };
          e.verifyLogin(n).then(function (e) {
            e.data && 20 == e.data.loginState
              ? (wx.setStorageSync('accessToken', e.data.accessToken),
                wx.setStorageSync('userId', e.data.userId),
                wx.navigateBack(),
                a.setData({ countdown: !0, count: 60 }))
              : a.setData({ errorMsg: e.msg });
          });
        },
      }),
      e.verifyLogin(obj).then(function (e) {
        e.data && 20 == e.data.loginState
          ? (wx.setStorageSync('accessToken', e.data.accessToken),
            wx.setStorageSync('userId', e.data.userId),
            wx.navigateBack(),
            t.setData({ countdown: !0, count: 60 }))
          : t.setData({ errorMsg: e.msg });
      });
  },
  openAgree: function () {
    wx.navigateTo({ url: '/pages/agreement/agreement' });
  },
  openPrivacy: function () {
    wx.navigateTo({ url: '/pages/privacy/privacy' });
  },
});
