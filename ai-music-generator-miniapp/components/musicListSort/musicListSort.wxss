.music_list {
  color: #fff;
  padding-bottom: 40rpx;
}
.music_list .title {
  font-size: 40rpx;
  padding: 20rpx;
}
.music_list_box {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding: 16rpx 20rpx;
  position: relative;
}
.music_list_box .coverimg {
  border-radius: 10rpx;
  height: 120rpx;
  margin-right: 20rpx;
  vertical-align: middle;
  width: 120rpx;
}
.music_list_box .cover {
  position: relative;
}
.music_list_box .version {
  color: #e2e8f0;
  margin-right: 16rpx;
}
.music_list_box .fullsong,
.music_list_box .part,
.music_list_box .version {
  background: rgba(226, 232, 240, 0.16);
  border-radius: 6rpx;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
}
.music_list_box .fullsong {
  background: #fbd38d;
  color: #000;
}
.music_list_box .share {
  padding: 10px 15px;
  position: absolute;
  right: 100rpx;
  top: 24rpx;
}
.music_list_box .share-btn {
  background: transparent;
  font-size: 24rpx;
  width: 16px;
}
.music_list_box .share-btn .img {
  width: 16px;
}
.music_list_box .more {
  padding: 10px 15px;
  position: absolute;
  right: 40rpx;
  top: 32rpx;
}
.music_list_box .more .img {
  height: 28rpx;
  width: 28rpx;
}
.music_list_box .part .img {
  height: 12px;
  vertical-align: sub;
  width: 12px;
}
.music_list_box .btn {
  height: 28rpx;
  left: 50%;
  margin: -14px 0 0-13px;
  position: absolute;
  top: 50%;
  width: 28rpx;
}
.music_list_box .tips {
  position: relative;
  top: -2px;
}
.music_list_box .name,
.music_list_box .style {
  font-size: 28rpx;
  max-width: 420rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.music_list_box .style {
  color: #cbd5e0;
  font-size: 24rpx;
}
.block {
  height: 130rpx;
}
.myAudio {
  display: none;
}
.user-class .music_list_box .name,
.user-class .music_list_box .style {
  line-height: 18px;
}
.vote-btn {
  background: #fbd38d;
  border-radius: 10rpx;
  color: #000;
  margin: 10rpx 10rpx 0 0;
  padding: 8rpx 10rpx;
}
.vote .num,
.vote-btn {
  font-size: 24rpx;
}
