{"appid": "wxb74afc75f2c20b65", "compileType": "miniprogram", "libVersion": "3.4.2", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}