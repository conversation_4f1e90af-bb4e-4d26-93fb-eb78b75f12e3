import logger from "@/lib/logger";
import {
  getMusicIdsExistAllOss,
  updateOssUrls,
  updateOssUrlsV2,
} from "@/models/music";
import { processMusicFileV2, processResources } from "@/services/music";
import { getMusicsFromFile } from "@/services/music";
import { Music } from "@/types/music";
import { setTimeout } from "timers";

export async function POST() {
  try {
    const existMusicIds = await getMusicIdsExistAllOss();
    const musics = await getMusicsFromFile();
    const musicsCount = musics.length;
    logger.info(
      `musicsCount: ${musicsCount}, exist music count: ${existMusicIds.length}`
    );

    // 分批并行处理任务，每批处理5个，每次间隔5秒
    const results = await batchProcessTasks(musics, existMusicIds, 5, 5000);

    // 统计任务结果
    const summary = results.reduce(
      (acc: { [key: string]: number }, result) => {
        const key =
          result.status === "fulfilled"
            ? result.value.status + "_count"
            : "failed_count";
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      },
      {
        success_count: 0,
        failed_count: 0,
        skipped_count: musics.length - results.length,
      }
    );

    logger.info("update-oss rsp=", JSON.stringify(summary));
    return Response.json({ data: summary });
  } catch (error) {
    logger.error("update-oss error=", error);
    return Response.json({ error: error });
  }
}

async function batchProcessTasks(
  musics: Music[],
  existMusicIds: string[],
  batchSize: number,
  delay: number
) {
  let tasks = [];
  let results = [];

  for (let i = 0; i < musics.length; i += batchSize) {
    const batch = musics.slice(i, Math.min(i + batchSize, musics.length));
    const batchTasks = batch.map((music) => {
      if (!music.music_id || existMusicIds.includes(music.music_id)) {
        return Promise.resolve({ status: "skipped", id: music.music_id });
      }
      return processResources(music)
        .then(() => updateOssUrlsV2(music))
        .then(() => ({ status: "success", id: music.music_id }))
        .catch((error) => {
          logger.error(
            "update music failed: ",
            music.music_id,
            music.title,
            error
          );
          return { status: "failed", id: music.music_id };
        });
    });
    tasks.push(...batchTasks);

    // Wait for the current batch to be settled and then delay the next batch
    const batchResults = await Promise.allSettled(batchTasks);
    results.push(...batchResults);
    await new Promise((resolve) => setTimeout(resolve, delay));
  }
  return results;
}
