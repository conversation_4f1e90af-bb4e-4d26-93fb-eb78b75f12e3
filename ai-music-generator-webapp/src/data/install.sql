-- 用户信息表
CREATE TABLE users (
    id SERIAL PRIMARY KEY, -- PK
    user_id UUID UNIQUE NOT NULL, -- 用户ID
    email VARCHAR(255), -- 邮箱
    nickname VARCHAR(255), -- 昵称
    avatar_url VARCHAR(255), -- 头像
    phone VARCHAR(20), -- 电话
    coin VARCHAR(20), 
    max_running_job INT, -- 最大并行任务数
    is_subscriber INT, -- 是否订阅
    bind_email VARCHAR(255), -- 绑定邮箱
    sensitive_flag INT, -- 敏感标记
    set_password VARCHAR(50), -- 设置密码
    is_ios_pay INT, -- 是否iOS支付
    created_at TIMESTAMPTZ, -- 创建时间
    updated_at TIMESTAMPTZ -- 更新时间
);

ALTER TABLE users
ADD COLUMN is_buy_limit_once BOOLEAN; -- 是否购买单次限购

-- 用户信息表创建索引
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);

-- 用户认证表
CREATE TABLE user_auths (
    id SERIAL PRIMARY KEY, -- PK
    user_id UUID UNIQUE NOT NULL, -- 用户ID
    auth_type VARCHAR(50), -- 认证类型
    open_id VARCHAR(255) UNIQUE, -- 三方应用唯一标识
    credential VARCHAR(255), -- 三方应用颁发的凭证
    union_id VARCHAR(255), -- wx unionid
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

-- 创建索引
CREATE INDEX idx_user_auths_user_id ON user_auths(user_id);
CREATE INDEX idx_user_auths_open_id ON user_auths(open_id);

-- 歌曲元数据信息表
CREATE TABLE music_metadatas (
    id SERIAL PRIMARY KEY, -- PK
    music_id UUID UNIQUE NOT NULL, -- 歌曲ID
    user_id UUID NOT NULL, -- 用户ID
    tags VARCHAR(255), -- 标签
    prompt TEXT, -- 提示词(歌词)
    gpt_description_prompt TEXT, -- GPT提示词
    audio_prompt_id VARCHAR(50), -- 音频提示ID
    history TEXT, -- 历史记录
    concat_history JSONB, -- 拼接记录
    type VARCHAR(10), -- 类型
    duration FLOAT, -- 时长
    refund_credits BOOLEAN, -- 退款积分
    stream BOOLEAN, -- 流
    error_type VARCHAR(50), -- 错误类型
    error_message TEXT, -- 错误信息
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

CREATE INDEX idx_music_metadatas_music_id ON music_metadatas(music_id);
CREATE INDEX idx_music_metadatas_user_id ON music_metadatas(user_id);

-- 歌曲信息表
CREATE TABLE musics (
    id SERIAL PRIMARY KEY, -- PK
    music_id UUID UNIQUE NOT NULL, -- 歌曲ID
    user_id UUID NOT NULL, -- 用户ID
    video_url VARCHAR(255), -- 视频链接
    audio_url VARCHAR(255), -- 音频链接
    image_url VARCHAR(255), -- 图片链接
    oss_video_url VARCHAR(255), -- 视频链接
    oss_audio_url VARCHAR(255), -- 音频链接
    oss_image_url VARCHAR(255), -- 图片链接
    image_large_url VARCHAR(255), -- 大图链接
    major_model_version VARCHAR(10), -- 主要模型版本
    model_name VARCHAR(50), -- 模型名称
    is_liked BOOLEAN, -- 是否喜欢
    display_name VARCHAR(50), -- 显示名称
    is_trashed BOOLEAN, -- 是否已删除
    reaction TEXT, -- 反应
    status VARCHAR(20), -- 状态。submitted=已提交 streaming=生成中 complete=已完成 error=错误
    title VARCHAR(100), -- 标题
    play_count INT, -- 播放次数
    upvote_count INT, -- 点赞次数
    is_public BOOLEAN, -- 是否公开
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

ALTER TABLE musics
ADD COLUMN is_retry BOOLEAN; -- 是否需要重试

CREATE INDEX idx_musics_music_id ON musics(music_id);
CREATE INDEX idx_musics_user_id ON musics(user_id);
CREATE INDEX idx_musics_status ON musics(status);
CREATE INDEX idx_musics_is_retry ON musics(is_retry);

-- 任务信息表
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY, -- PK
    task_id UUID NOT NULL, -- 任务ID
    music_ids UUID[] NOT NULL, -- 音乐ID
    user_id UUID NOT NULL, -- 用户ID
    clips JSONB, -- 片段数据
    status VARCHAR(20), -- 状态。queue, running, complete
    batch_size SMALLINT, -- 一组大小
    major_model_version VARCHAR(10), -- 主要模型版本
    metadata JSONB, -- 元数据
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

CREATE INDEX idx_tasks_user_id ON tasks(user_id);

-- URL日志表
CREATE TABLE url_logs (
    id SERIAL PRIMARY KEY, -- PK
    url VARCHAR(255) UNIQUE, -- 文件URL
    is_processed BOOLEAN NOT NULL DEFAULT true,  -- 默认为true，表示已处理
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

CREATE INDEX idx_url_logs_url ON url_logs(url);

-- 订阅包配置表
CREATE TABLE subscribe_package_cfgs (
    id SERIAL PRIMARY KEY, -- PK
    package_id UUID NOT NULL, -- 订阅包ID
    package_name VARCHAR(100) NOT NULL, -- 订阅包名称
    package_type VARCHAR(20), -- 订阅包类型 dev test prod
    package_price VARCHAR(20) NOT NULL, -- 订阅包价格：展示用
    amount INT NOT NULL, -- 订阅包价格，单位为分：计算用
    currency VARCHAR(10), -- 货币 CNY=人民币 USD=美元
    period VARCHAR(20), -- 周期 once=一次性 monthly=每月 quarterly=每季 yearly=每年
    credits INT NOT NULL, -- 积分
    attributes TEXT[] -- 属性：介绍文本列表
);

ALTER TABLE subscribe_package_cfgs
ADD COLUMN is_limit_once BOOLEAN; -- 是否单次限购

CREATE INDEX idx_subscribe_package_cfgs_package_id ON subscribe_package_cfgs(package_id);
CREATE INDEX idx_subscribe_package_cfgs_package_type ON subscribe_package_cfgs(package_type);
CREATE INDEX idx_subscribe_package_cfgs_is_limit_once ON subscribe_package_cfgs(is_limit_once);

-- 订单信息表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY, -- PK
    order_id UUID NOT NULL, -- 订单编号
    user_id UUID NOT NULL, -- 用户ID
    package_id UUID NOT NULL, -- 订阅包ID
    out_trade_no VARCHAR(64) UNIQUE NOT NULL, -- 商户订单号
    transaction_id VARCHAR(64), -- 微信订单号
    nonce_str VARCHAR(64), -- 随机字符串
    prepay_id VARCHAR(64), -- 支付产生的id
    pay_type SMALLINT NOT NULL, -- 支付方式 0=wxpay 1=alipay
    pay_status SMALLINT NOT NULL, -- 支付状态 0=创建 1=成功 2=失败 3=取消
    amount INT NOT NULL, --- 订单金额，单位为分。
    currency VARCHAR(10), -- 货币
    period VARCHAR(20), -- 周期 once=一次性 monthly=月度 
    credits INT NOT NULL, -- 积分
    paied_at TIMESTAMPTZ, -- 支付时间
    expired_at TIMESTAMPTZ, -- 到期时间
    created_at TIMESTAMPTZ, -- 创建时间
    updated_at TIMESTAMPTZ -- 更新时间
);

CREATE INDEX idx_orders_order_id ON orders(order_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_out_trade_no ON orders(out_trade_no);
CREATE INDEX idx_orders_pay_status ON orders(pay_status);
