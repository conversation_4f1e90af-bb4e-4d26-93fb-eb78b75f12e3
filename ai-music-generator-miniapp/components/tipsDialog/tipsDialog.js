Component({
  data: { dialogShow: !1 },
  pageLifetimes: {
    show: function () {
      var t = this,
        o = wx.getStorageSync("tipsFlagVal");
      wx.getStorageSync("accessToken") &&
        setTimeout(function () {
          t.setData({ dialogShow: "1" != o });
        }, 2e3);
    },
  },
  methods: {
    onSure: function () {
      this.setData({ dialogShow: !1 }), wx.setStorageSync("tipsFlagVal", "1");
    },
    onCancel: function () {
      wx.exitMiniProgram({
        success: function (t) {
          console.log("关闭成功");
        },
      });
    },
  },
});
