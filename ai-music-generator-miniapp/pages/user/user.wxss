.user {
  background: #232323;
  box-sizing: border-box;
  color: #fff;
  height: 100vh;
}
.bg-image {
  position: fixed;
  top: 0;
  z-index: 0;
}
.user-header {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding: 30rpx 30rpx 0;
}
.logout {
  background: #272c2b;
  border-radius: 40rpx;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  position: absolute;
  right: 30rpx;
}
.user-header .img {
  border-radius: 50%;
  width: 90rpx;
}
.user-header .login {
  background: transparent;
  padding: 10px 0;
  width: auto;
}
.user-header .login,
.user-header .userinfo {
  color: #f7efe5;
  font-size: 28rpx;
  margin-left: 25rpx;
}
.vipcount {
  background: #313235;
  border-radius: 10rpx;
  font-size: 28rpx;
  margin: 30rpx 30rpx 0;
  padding: 20rpx;
}
.vipcount .monthCoinDate {
  display: -webkit-flex;
  display: flex;
  justify-content: flex-end;
}
.customer {
  justify-content: space-between;
  margin-top: 10rpx;
}
.customer .img {
  transform: rotate(180deg);
  width: 35rpx;
}
.user-vip {
  background: linear-gradient(90deg, #342828, #23302e);
  border-radius: 50rpx;
  color: #ff5656;
  font-size: 28rpx;
  justify-content: center;
  margin: 30rpx;
  padding: 20rpx 30rpx;
  width: 50%;
}
.user-flex,
.user-vip {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.user-flex {
  justify-content: space-between;
}
.customer {
  align-items: center;
  background: linear-gradient(90deg, #23302e, #342828);
  border-radius: 50rpx;
  color: #fff;
  display: -webkit-flex;
  display: flex;
  font-size: 28rpx;
  justify-content: center;
  margin: 30rpx;
  opacity: 0.6;
  padding: 20rpx 30rpx;
  width: 50%;
}
.user-vip .text {
  font-size: 22rpx;
  margin-left: 8rpx;
  vertical-align: middle;
}
.user-vip .btn {
  border: 1px solid #d9b68c;
  border-radius: 50rpx;
  font-size: 22rpx;
  padding: 6rpx 20rpx;
}
.tabs {
  display: -webkit-flex;
  display: flex;
  gap: 30rpx;
  margin-bottom: 10px;
  margin-left: 20rpx;
}
.tabs .tab {
  font-size: 26rpx;
  opacity: 0.5;
}
.tabs .tab.active {
  border-bottom: 2px solid #ff3a3a;
  border-radius: 2rpx;
  font-size: 26rpx;
  opacity: 1;
  padding-bottom: 10rpx;
}
.gotop {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid hsla(0, 0%, 100%, 0.2);
  border-radius: 50%;
  bottom: 120rpx;
  display: none;
  padding: 7rpx 16rpx;
  position: fixed;
  right: 10rpx;
  text-align: center;
}
.gotop.active {
  display: block;
}
.gotop .img {
  width: 30rpx;
}
.music_list {
  color: #fff;
}
.music_list .title {
  font-size: 40rpx;
  padding: 20rpx;
}
.music_list_box {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding: 10px;
}
.music_list_box .cover_img,
.music_list_box .cover_img .img {
  border-radius: 10rpx;
  height: 120rpx;
  margin-right: 20rpx;
  vertical-align: middle;
  width: 120rpx;
}
.music_list_box .loading {
  align-items: center;
  background: #100f0f3d;
  border-radius: 10rpx;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  gap: 1rpx;
  height: 120rpx;
  justify-content: center;
  margin-right: 20rpx;
  position: absolute;
  top: 0;
  width: 120rpx;
}
.music_list_box .loading .loadind_img {
  animation: rotating 0.3s linear infinite;
  height: 30rpx;
  width: 30rpx;
}
.music_list_box .loading .progress {
  font-size: 26rpx;
}
.music_list_box .cover {
  position: relative;
}
.music_list_box .btn {
  height: 28rpx;
  left: 50%;
  margin: -14px 0 0-13px;
  position: absolute;
  top: 50%;
  width: 28rpx;
}
.music_list_box .name,
.music_list_box .style {
  max-width: 560rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.music_list_box .style {
  color: #cbd5e0;
  font-size: 28rpx;
}
.music_list_box .text {
  animation: elskeletonloadind 2s linear infinite;
  background: #282e36;
  border-radius: 10px;
  height: 10px;
  margin: 10px 10px 10px 0;
  overflow: hidden;
  position: relative;
  width: 220px;
}
.music_list_box .text.text1 {
  width: 150px;
}
.music_list_box .text::after {
  animation: loadingBar 5.6s linear 0s infinite normal none running;
  background: linear-gradient(
    90deg,
    rgba(43, 132, 255, 0),
    hsla(0, 0%, 79%, 0.3) 49.56%,
    rgba(43, 132, 255, 0)
  );
  content: '';
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 220px;
}
.music_list_box .text.text1::after {
  width: 150px;
}
.block {
  height: 130rpx;
}
.nodata {
  color: #636161;
}
.nodata,
.tips {
  font-size: 24rpx;
  text-align: center;
}
.tips {
  color: #888787;
  margin-top: 20rpx;
}
@-webkit-keyframes loadingBar {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes loadingBar {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}
.music_list_box .name {
  font-size: 26rpx;
}
.operate-page {
  color: #ffffffd5;
  padding: 20rpx 40rpx;
}
.operate-page .item {
  font-size: 28rpx;
  margin: 25rpx 0;
}
.operate-page .hr {
  border-bottom: 1rpx solid hsla(0, 4%, 85%, 0.16);
}
.operate-page .copy {
  color: #a1a1a1;
  float: right;
  font-size: 24rpx;
}
.operate-page .copy .img {
  display: inline-block;
  height: 24rpx;
  position: relative;
  top: -1px;
  width: 24rpx;
}
.refreshTips {
  color: #ccc;
  font-size: 24rpx;
  padding: 20px 0;
  text-align: center;
}
.prompt-dialog .weui-dialog__hd {
  padding: 10px 0;
}
.prompt-dialog .weui-dialog {
  background: #000;
  background: #131314;
  border: 1px solid #222;
  border-radius: 10rpx;
  transform: translate(0, -50%);
}
.prompt-dialog .weui-dialog__title {
  color: #fff;
  font-weight: 400;
  padding-top: 10px;
}
.prompt-dialog .weui-dialog__bd {
  color: hsla(0, 0%, 100%, 0.95);
  font-size: 28rpx;
  margin-bottom: 0;
}
.prompt-dialog .deleteimg .btn {
  background-color: #ff3a3a;
  border-radius: 60rpx;
  color: #000;
  font-size: 26rpx;
  font-weight: 400;
  margin: 20rpx;
  padding: 10rpx 0;
  width: 80px;
}
.prompt-dialog .deleteimg .btn.cancel {
  background: #3a3939c2;
  color: #fff;
}
.name {
  line-height: 18px;
}
