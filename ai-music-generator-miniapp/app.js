var e = require('./services/index');
App({
  onLaunch: function () {
    var t = wx.getStorageSync('logs') || [];
    t.unshift(Date.now()),
      wx.setStorageSync('logs', t),
      (wx.getStorageSync('accessToken') || '') &&
        e.getUser().then(function (e) {
          0 === e.code && wx.setStorageSync('userInfo', e.data);
        });
  },
  globalData: {
    isMusicPlay: !1,
    musicId: '',
    topId: '',
    songlist: [],
    currentIndex: '',
    currentSongClipId: '',
  },
  watch: function (e) {
    var t = this.globalData;
    Object.defineProperty(this, 'globalData', {
      configurable: !0,
      enumerable: !0,
      set: function (n) {
        (t.type = n.type), e(n);
      },
      get: function () {
        return t;
      },
    });
  },
});
