import { respData, respErr } from "@/lib/resp";
import { getRunningTaskIds } from "@/models/task";

// 获取运行任务ID列表
export async function POST(req: Request) {
  try {
    console.log(`---listRunningTaskIds req=${JSON.stringify(req.json)}`);

    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const task_ids: string[] = await getRunningTaskIds(user_id);

    console.log(`---listRunningTaskIds rsp=${JSON.stringify(task_ids)}`);

    return respData(task_ids);
  } catch (e) {
    console.error("listRunningTaskIds error=", e);
    return respErr("listRunningTaskIds");
  }
}
