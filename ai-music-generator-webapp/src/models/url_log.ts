import logger from "@/lib/logger";
import { getDb } from "@/models/db";

// 检查URL是否已处理
export async function checkUrlIsProcessed(url: string): Promise<boolean> {
  const db = getDb(); // 获取数据库连接
  const query = "SELECT is_processed FROM url_logs WHERE url = $1";
  const params = [url] as any; // SQL 参数

  try {
    // logger.info("Executing SQL", { query, params }); // 日志记录执行的SQL和参数
    const res = await db.query(query, params); // 执行查询操作
    // logger.info("SQL Query Result", { rows: res.rows }); // 日志记录查询结果
    return res.rows.length > 0 && res.rows[0].is_processed;
  } catch (error) {
    logger.error("Error in checkUrlIsProcessed", { query, params, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error checking processed status for url ${url}: ${error}`); // 抛出错误，以供上层处理
  }
}

// 标记URL的处理状态（upsert更新或新增）
export async function markUrlIsProcessed(
  url: string,
  is_processed: boolean
): Promise<void> {
  const db = getDb(); // 获取数据库连接
  // 使用 ON CONFLICT 语句实现 upsert 功能
  const query = `
    INSERT INTO url_logs (url, is_processed, created_at, updated_at) 
    VALUES ($1, $2, NOW(), NOW())
    ON CONFLICT (url) DO UPDATE 
    SET is_processed = EXCLUDED.is_processed, updated_at = NOW();
  `;
  const params = [url, is_processed] as any; // SQL 参数

  try {
    // logger.info("Executing SQL", { query, params }); // 日志记录执行的SQL和参数
    const result = await db.query(query, params); // 执行更新操作
    // logger.info("SQL Result", { rowCount: result.rowCount }); // 日志记录SQL执行结果
  } catch (error) {
    logger.error("Error in markUrlIsProcessed", { query, params, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error updating processed status for url ${url}: ${error}`); // 抛出错误，以供上层处理
  }
}
