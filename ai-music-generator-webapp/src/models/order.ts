import { Order } from "@/types/order";
import { PayType, PayStatus, PeriodType } from "@/types/common";
import { QueryResultRow } from "pg";
import { getDb } from "@/models/db";
import logger from "@/lib/logger";

export async function insertOrder(order: Order) {
  const db = getDb();
  const query = `INSERT INTO orders (
    order_id, user_id, package_id, out_trade_no, transaction_id,
    nonce_str, prepay_id, pay_type, pay_status, amount, currency,
    period, credits, paied_at, expired_at, created_at, updated_at
  ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)`;
  const params = [
    order.orderId,
    order.userId,
    order.packageId,
    order.outTradeNo,
    order.transactionId,
    order.nonceStr,
    order.prepayId,
    order.payType,
    order.payStatus,
    order.amount,
    order.currency,
    order.period,
    order.credits,
    order.paiedAt,
    order.expiredAt,
    order.createdAt,
    order.updatedAt,
  ] as any;
  try {
    const result = await db.query(query, params);
    logger.info("insertOrder:", result.rowCount);
  } catch (error) {
    logger.error("Error insertOrder", { query, params, error });
    throw new Error(`Error insertOrder ${order}: ${error}`);
  }
}

export async function findOrderByOrderId(
  order_id: string
): Promise<Order | undefined> {
  const db = getDb();
  const query = `SELECT * FROM orders WHERE order_id = $1 LIMIT 1`;
  const params = [order_id];
  try {
    const res = await db.query(query, params);
    if (res.rowCount === 0) {
      return undefined;
    }
    const row = res.rows[0];
    const order = formatOrder(row);
    return order;
  } catch (error) {
    logger.error("Error in findOrderByOrderId", { query, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error findOrderByOrderId ${error}`); // 抛出错误，以供上层处理
  }
}

export async function updateOrderStatus(
  out_trade_no: string,
  pay_status: PayStatus,
  paied_at: string
) {
  const updated_at: string = new Date().toISOString();
  const db = getDb();
  const query = `UPDATE orders SET pay_status=$1, paied_at=$2, updated_at=$3 WHERE out_trade_no=$4`;
  const params = [pay_status, paied_at, updated_at, out_trade_no];
  try {
    const result = await db.query(query, params);
    if (result.rowCount === 0) {
      throw new Error(`Order with out_trade_no=${out_trade_no} not found.`);
    }
    logger.info(`Order ${out_trade_no} updated ${pay_status} successfully.`);
    return true;
  } catch (error) {
    logger.error("Error in updateOrderStatus", { query, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error updateOrderStatus ${error}`); // 抛出错误，以供上层处理
  }
}

export async function getUserOrders(
  user_id: string
): Promise<Order[] | undefined> {
  const now = new Date().toISOString();
  const db = getDb();
  const query = `
  SELECT * FROM orders
  WHERE user_id = $1 
    AND pay_status = $2 
    AND (period = 'once' OR expired_at >= $3)
  ORDER BY id ASC`;
  const params = [user_id, PayStatus.Success, now];
  try {
    const res = await db.query(query, params);
    if (res.rowCount == 0) {
      return undefined;
    }
    let orders: Order[] = [];
    const { rows } = res;
    rows.forEach((row) => {
      const order = formatOrder(row);
      orders.push(order);
    });
    return orders;
  } catch (error) {
    logger.error("Error getUserOrders", { query, error });
    throw new Error(`Error getUserOrders ${error}`);
  }
}

function formatOrder(row: QueryResultRow): Order {
  const order: Order = {
    id: row.id,
    orderId: row.order_id,
    userId: row.user_id,
    packageId: row.package_id,
    outTradeNo: row.out_trade_no,
    transactionId: row.transaction_id,
    nonceStr: row.nonce_str,
    prepayId: row.prepay_id,
    payType: row.pay_type,
    payStatus: row.pay_status,
    amount: row.amount,
    currency: row.currency,
    period: row.period,
    credits: row.credits,
    paiedAt: row.paied_at,
    expiredAt: row.expired_at,
    createdAt: row.created_at,
    updatedAt: row.update_at,
  };
  return order;
}
