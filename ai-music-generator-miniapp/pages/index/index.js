getApp();

// var e = require("../../utils/http.js");
var e = require("../../services/index");

Page({
    data: {
        canIUse: wx.canIUse("button.open-type.getUserInfo"),
        hasNet: !0
    },
    onLoad: function(t) {
        var n = this;
        wx.request({
            url: e.serverDomain + "/",
            success: function(e) {
                n.data.hasNet = !0, wx.getSetting({
                    success: function(e) {
                        e.authSetting["scope.userInfo"] && (wx.getUserInfo({
                            success: function(e) {
                                wx.setStorageSync("userInfo", e.userInfo);
                            }
                        }), wx.getStorageSync("userId") && (wx.showLoading({
                            title: "登录中...",
                            complete: function(e) {
                                wx.redirectTo({
                                    url: "../home/<USER>"
                                });
                            }
                        }), setTimeout(function() {
                            wx.hideLoading();
                        }, 2e3)));
                    }
                });
            },
            fail: function(e) {
                n.data.hasNet = !1, wx.showToast({
                    title: "网络连接失败！",
                    icon: "none",
                    duration: 2e3
                });
            }
        }), wx.getSystemInfo({
            success: function(e) {
                n.setData({
                    winWidth: e.windowWidth,
                    winHeight: e.windowHeight
                });
            }
        });
    },
    bindGetUserInfo: function(t) {
        wx.getSetting({
            success: function(t) {
                t.authSetting["scope.userInfo"] ? (wx.showLoading({
                    title: "登录中..."
                }), wx.login({
                    success: function(t) {
                        t.code && wx.getUserInfo({
                            success: function(n) {
                                wx.setStorageSync("userInfo", n.userInfo), wx.request({
                                    url: e.getOpenId,
                                    data: {
                                        code: t.code,
                                        nickname: n.userInfo.nickName,
                                        avatarUrl: n.userInfo.avatarUrl,
                                        gender: n.userInfo.gender,
                                        country: n.userInfo.country,
                                        province: n.userInfo.province,
                                        city: n.userInfo.city
                                    },
                                    success: function(e) {
                                        wx.setStorageSync("userId", e.data.userId), wx.redirectTo({
                                            url: "../home/<USER>"
                                        });
                                    },
                                    fail: function(e) {
                                        wx.hideLoading(), wx.showToast({
                                            title: "联网失败!",
                                            icon: "none",
                                            duration: 1500
                                        });
                                    }
                                });
                            }
                        });
                    }
                })) : wx.showToast({
                    title: "请允许授权才能使用小程序哦~",
                    icon: "none"
                });
            }
        });
    },
    toXZYY: function() {}
});