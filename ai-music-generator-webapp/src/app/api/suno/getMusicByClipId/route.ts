import { respData, respErr } from "@/lib/resp";
import { getMusicByMusicId, getMusics } from "@/models/music";
import logger from "@/lib/logger";

// 获取探索歌曲列表
export async function POST(req: Request) {
  try {
    logger.info(`---getMusicByClipId req=${JSON.stringify(req.json)}`);

    const { id, from } = await req.json();

    // get music
    const music = await getMusicByMusicId(id, from);

    logger.info(`---getMusicByClipId rsp=${JSON.stringify(music)}`);

    return respData(music);
  } catch (e) {
    console.error("getMusicByClipId error=", e);
    return respErr("getMusicByClipId");
  }
}
