.iconfont {
    font-family: "iconfont"!important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
    content: "\e637";
}

.icon-wenhao:before {
    content: "\e71d";
    color: #fff;
    margin-left: 20rpx;
}

.nav-wrap {
    position: fixed;
    width: 100%;
    height: 32px;
    padding: 15rpx 0;
    top: 0;
    z-index: 8;
}

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #fff;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 10rpx;
    width: 570rpx;
    justify-content: space-between;
    height: 32px;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #e5e5e5;
}

.back-box {
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-box .iconfont {
    font-size: 40rpx;
    color: #fff;
}

.back-pre,.back-home {
    width: 50rpx;
    height: 50rpx;
}

.nav-capsule .back-home {
    width: 36rpx;
    height: 40rpx;
    margin-top: 3rpx;
}

.song_box {
    width: 100%;
    height: 32px;
    padding: 15rpx 0;
    padding-left: 10rpx;
    display: flex;
    align-items: center;
}

.title-txt {
    color: #fff;
    font-size: 36rpx;
}

.name_txt {
    color: #fff;
    display: flex;
    align-items: center;
    font-size: 34rpx;
    margin-bottom: 15rpx;
}

.author_txt {
    color: #c9c9c9;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    margin-bottom: 5rpx;
}