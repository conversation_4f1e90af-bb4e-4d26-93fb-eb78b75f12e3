# 🎵 AI 音乐生成器模版

> 开箱即用的 AI 音乐生成器模版（已支持微信小程序和网页端）

![preview](/imgs/preview.png)

## ✨ 示例

> 服务维护中

在微信小程序中搜索“音缘”

或者在微信中扫描下方小程序码 👇

![yy_mp_code](/imgs/yy_mp_code.png)

### 展示视频

https://github.com/user-attachments/assets/cc02cdc2-b121-45c7-b0e9-def6e25549e9

## 🎯 核心功能

- **AI 音乐生成**: 使用 Suno AI 生成高质量可商用的音乐
- **便捷登录**: 使用微信或手机号登入
- **安全支付**: 支持微信支付
- **商业变现**: 可配置的定价方案和优惠活动
- **精美界面**: 流畅友好的生成音乐和播放音乐
- **实用功能**: 分享转发，下载音频，下载视频
- **一键部署**: 支持一键部署服务到 Vercel

## 🔬 关键技术

- 微信小程序原生: wxml, wxss, javascript（类似 HTML, CSS, JS）
- NextJs: TypeScript
- 七牛云存储: 存储图片/音频/视频等资源

## 📀 快速开始

克隆代码：

```bash
git clone https://github.com/Kevin-free/ai-music-generator ai-music-generator
```

### 微信小程序

1. 下载微信开发者工具
2. 打开微信开发者工具，选择“本地小程序项目”，然后选择“添加项目”
3. 选择“从本地导入”，然后选择“ai-music-generator-miniapp”文件夹
4. 点击“导入”
5. 点击“编译”
6. 点击“预览”
7. 用微信扫二维码即可在微信中预览

### 网页端

1. 进入项目根目录

```bash
cd ai-music-generator-webapp
```

2. 安装项目依赖

```bash
pnpm install
```

3. 复制环境变量配置文件

```bash
cp .env.example .env.development
```

4. 启动开发服务器

```bash
pnpm dev
```
