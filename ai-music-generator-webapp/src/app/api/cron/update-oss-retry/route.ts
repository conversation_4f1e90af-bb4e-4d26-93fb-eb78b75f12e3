import { NextResponse } from "next/server";
import logger from "@/lib/logger";
import { getMusicsIsRetry, updateMusicIsRetry } from "@/models/music";
import { checkUrlIsProcessed } from "@/models/url_log";
import { processOssUrl } from "@/services/music";

// 定义API的返回类型
interface APIResponse {
  totalCount: number;
  successCount: number;
  failedCount: number;
  errors: string[];
}

export const revalidate = 0

export async function GET() {
  try {
    // 获取需要重试的音乐记录
    const musics = await getMusicsIsRetry();
    let musicsCount = musics.length;
    // logger.info(`update-oss-retry musicsCount: ${musicsCount}`);

    let successCount = 0; // 成功处理的数量
    let failedCount = 0; // 处理失败的数量
    const errors: string[] = []; // 错误信息数组

    for (const music of musics) {
      // 标记音乐记录为不再需要重试
      await updateMusicIsRetry(music.music_id, false);

      const mediaTasks = [];
      // 检查视频URL是否已处理
      if (music.video_url && !(await checkUrlIsProcessed(music.video_url))) {
        mediaTasks.push(processOssUrl(music.video_url, "video", music));
      }
      // 检查音频URL是否已处理
      if (music.audio_url && !(await checkUrlIsProcessed(music.audio_url))) {
        mediaTasks.push(processOssUrl(music.audio_url, "audio", music));
      }
      // 检查图片URL是否已处理
      if (music.image_url && !(await checkUrlIsProcessed(music.image_url))) {
        mediaTasks.push(processOssUrl(music.image_url, "image", music));
      }

      // 等待所有媒体处理任务完成
      const results = await Promise.allSettled(mediaTasks);

      // 分析每个任务的结果，计算成功和失败的数量
      results.forEach((result) => {
        if (result.status === "fulfilled") {
          successCount++;
        } else {
          failedCount++;
          errors.push(result.reason);
        }
      });
    }

    // 创建并返回成功的响应
    const response: APIResponse = {
      totalCount: musicsCount,
      successCount,
      failedCount,
      errors,
    };
    logger.info(`update-oss-retry rsp: ${JSON.stringify(response)}`);
    return NextResponse.json(response);
  } catch (error) {
    logger.error("update-oss-retry error: ", error);
    return NextResponse.json({ error: error });
  }
}
