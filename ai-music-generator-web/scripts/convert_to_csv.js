const fs = require('fs');
const path = require('path');

// 读取JSON数据
const jsonData = JSON.parse(fs.readFileSync(path.join(__dirname, '../src/db/init_data/suno.json'), 'utf8'));

// CSV转义函数
const escapeCsvValue = (value) => {
  if (value === null || value === undefined) return '';
  const str = String(value);
  if (str.includes(',') || str.includes('"') || str.includes('\n')) {
    return `"${str.replace(/"/g, '""')}"`;
  }
  return str;
};

// 转换数据格式
const transformedData = jsonData.map(item => {
  const metadata = item.metadata || {};
  
  return {
    uuid: item.id,
    user_uuid: item.user_id || 'default-user-uuid',
    tags: metadata.tags || null,
    prompt: metadata.prompt || null,
    gpt_description_prompt: metadata.gpt_description_prompt || null,
    audio_prompt_id: metadata.audio_prompt_id || null,
    history: metadata.history ? JSON.stringify(metadata.history) : null,
    concat_history: metadata.concat_history ? JSON.stringify(metadata.concat_history) : null,
    type: metadata.type || null,
    duration: metadata.duration || null,
    refund_credits: metadata.refund_credits || null,
    stream: metadata.stream || null,
    error_type: metadata.error_type || null,
    error_message: metadata.error_message || null,
    created_at: item.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
});

// 生成CSV内容
const headers = [
  'uuid', 'user_uuid', 'tags', 'prompt', 'gpt_description_prompt', 
  'audio_prompt_id', 'history', 'concat_history', 'type', 'duration',
  'refund_credits', 'stream', 'error_type', 'error_message', 
  'created_at', 'updated_at'
];

const csvContent = [
  headers.join(','),
  ...transformedData.map(row => 
    headers.map(header => escapeCsvValue(row[header])).join(',')
  )
].join('\n');

// 保存CSV文件
fs.writeFileSync(path.join(__dirname, 'songs_import.csv'), csvContent);
console.log(`Generated songs_import.csv with ${transformedData.length} records`);
