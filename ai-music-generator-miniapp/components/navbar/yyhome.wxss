.iconfont {
    font-family: "iconfont"!important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
    content: "\e637";
}

.icon-info:after {
    content: "\e643";
}

.nav-wrap {
    position: fixed;
    width: 100%;
    height: 32px;
    top: 0;
    background-color: #bb2c08;
    z-index: 18;
}

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    width: 550rpx;
    height: 32px;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #e5e5e5;
}

.back-box {
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 28;
}

.back-box .iconfont {
    font-size: 38rpx;
    color: #fff;
}

.title-box {
    width: 100%;
    height: 32px;
    position: fixed;
    display: flex;
    color: #ccc;
}

.title-box .active {
    color: #fff;
    font-weight: bolder;
    font-size: 38rpx;
}

.title-yy {
    width: 100rpx;
    height: 32px;
    position: fixed;
    right: 55%;
    align-items: center;
    justify-content: center;
    display: flex;
}

.title-my {
    width: 100rpx;
    height: 32px;
    position: fixed;
    left: 55%;
    align-items: center;
    justify-content: center;
    display: flex;
}