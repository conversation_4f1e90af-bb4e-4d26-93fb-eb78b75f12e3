var e = require("../../@babel/runtime/helpers/typeof");
module.exports = (function (t) {
  var n = {};
  function r(e) {
    if (n[e]) return n[e].exports;
    var i = (n[e] = { i: e, l: !1, exports: {} });
    return t[e].call(i.exports, i, i.exports, r), (i.l = !0), i.exports;
  }
  return (
    (r.m = t),
    (r.c = n),
    (r.d = function (e, t, n) {
      r.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: n });
    }),
    (r.r = function (e) {
      "undefined" != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }),
        Object.defineProperty(e, "__esModule", { value: !0 });
    }),
    (r.t = function (t, n) {
      if ((1 & n && (t = r(t)), 8 & n)) return t;
      if (4 & n && "object" === e(t) && t && t.__esModule) return t;
      var i = Object.create(null);
      if (
        (r.r(i),
        Object.defineProperty(i, "default", { enumerable: !0, value: t }),
        2 & n && "string" != typeof t)
      )
        for (var a in t)
          r.d(
            i,
            a,
            function (e) {
              return t[e];
            }.bind(null, a),
          );
      return i;
    }),
    (r.n = function (e) {
      var t =
        e && e.__esModule
          ? function () {
              return e.default;
            }
          : function () {
              return e;
            };
      return r.d(t, "a", t), t;
    }),
    (r.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t);
    }),
    (r.p = ""),
    r((r.s = 5))
  );
})({
  5: function (e, t, n) {
    Component({
      options: { addGlobalClass: !0, pureDataPattern: /^_/, multipleSlots: !0 },
      properties: {
        tabs: { type: Array, value: [] },
        label: { type: String, value: "" },
        tabClass: { type: String, value: "" },
        swiperClass: { type: String, value: "" },
        activeClass: { type: String, value: "" },
        tabUnderlineColor: { type: String, value: "#FBD38D" },
        tabActiveTextColor: { type: String, value: "#000000" },
        tabInactiveTextColor: { type: String, value: "#000000" },
        tabBackgroundColor: { type: String, value: "#ffffff" },
        activeTab: { type: Number, value: 0 },
        swipeable: { type: Boolean, value: !0 },
        animation: { type: Boolean, value: !0 },
        duration: { type: Number, value: 500 },
      },
      data: { currentView: 0, swiperHeight: [] },
      observers: {
        activeTab: function (e) {
          var t = this.data.tabs.length;
          if (0 !== t) {
            var n = e - 1;
            n < 0 && (n = 0),
              n > t - 1 && (n = t - 1),
              this.setData({ currentView: n }),
              console.log(this.data.swiperHeight);
          }
        },
        tabs: function (e) {
          this.data.tabs.length > 0 && this.getSwiperHeight();
        },
      },
      lifetimes: { created: function () {}, ready: function () {} },
      pageLifetimes: {
        show: function () {},
        hide: function () {},
        resize: function () {},
      },
      methods: {
        getSwiperHeight: function () {
          var e = this,
            t = [];
          setTimeout(function () {
            wx.createSelectorQuery()
              .in(e)
              .selectAll(".swiper-content")
              .boundingClientRect(function (n) {
                n.forEach(function (e, n) {
                  t[n] = 0 == e.height ? 250 : e.height;
                }),
                  e.setData({ swiperHeight: t });
              })
              .exec();
          }, 100);
        },
        handleTabClick: function (e) {
          var t = e.currentTarget.dataset.index;
          this.setData({ activeTab: t }),
            this.triggerEvent("tabclick", { index: t });
        },
        handleSwiperChange: function (e) {
          var t = e.detail.current;
          this.setData({ activeTab: t }),
            this.triggerEvent("change", { index: t });
        },
      },
    });
  },
});
