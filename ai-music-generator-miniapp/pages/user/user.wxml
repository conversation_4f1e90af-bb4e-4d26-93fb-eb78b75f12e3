<view class="user">
    <view class="user-header">
        <image bind:tap="scribeMessage" class="img" mode="widthFix" src="{{userInfo.headerUrl}} "></image>
        <view>
            <button bind:tap="openLogin" class="login" wx:if="{{!isLogin}}">登录/注册</button>
            <view class="userinfo" wx:else>
                <view class="name">{{userInfo.nickName}}</view>
            </view>
        </view>
        <view bindtap="onLogout" class="logout" wx:if="{{isLogin}}">退出登录</view>
    </view>
    <view class="vipcount" wx:if="{{isLogin}}">
        <view class="">
            <text>剩余积分：{{vipCount.leftCoin||0}}</text>
        </view>
        <view class="" wx:if="{{vipCount.monthCoinDate}}">
            <text>到期时间：{{vipCount.monthCoinDate}}</text>
            <text class="datelater" wx:if="{{isOverdue}}">（已过期）</text>
        </view>
    </view>
    <view class="user-flex">
        <view bindtap="openCustomer" class=" customer" wx:if="{{isLogin}}"> 联系客服 </view>
        <view bindtap="openVipPage" class="user-vip" wx:if="{{isLogin&&(isPay||iosPay)}}"> 会员充值 </view>
        <view class="user-vip" wx:if="{{isLogin&&!isPay&&!iosPay}}"> IOS暂不支持充值 </view>
    </view>
    <view class="tabs" wx:if="{{isLogin}}">
        <view bindtap="tabChange" class="tab {{tabIndex==1?'active':''}}" data-index="1">我的作品</view>
        <view bindtap="tabChange" class="tab {{tabIndex==2?'active':''}}" data-index="2">制作中</view>
    </view>
    <scroll-view bindrefresherabort="onAbort" bindrefresherpulling="onPulling" bindrefresherrefresh="onRefresh" bindrefresherrestore="onRestore" bindscroll="onScroll" bindscrolltolower="onReachBottom" refresherBackground="#000" refresherDefaultStyle="black" refresherEnabled="true" refresherTriggered="{{triggered}}" scrollTop="{{scrollTop}}" scrollY="true" style="height:{{scrollH}}px;" type="custom">
        <view class="refreshTips" wx:if="{{refreshTips}}"> {{refreshTips}} </view>
        <view class="list" wx:if="{{isLogin}}">
            <view class="tab-item" wx:if="{{tabIndex==1}}">
                <view class="nodata" wx:if="{{myList.length==0}}">还没有作品</view>
                <mp-musicList bind:myCustomEvent="onChildEvent" extClass="user-class" musicList="{{myList}}" wx:else></mp-musicList>
                <view class="nodata" wx:if="{{reachBottom}}"> ~ 到底啦 ~ </view>
            </view>
            <view class="tab-item" wx:if="{{tabIndex==2}}">
                <view class="nodata" wx:if="{{generatingList.length==0}}">还没有制作中的作品</view>
                <view class="music_list" wx:else>
                    <view class="tips">生成时间通常在2-3分钟，请耐心等待。</view>
                    <view class="music_list_box" wx:for="{{generatingList}}" wx:key="clipId">
                        <view class="cover">
                            <view class="cover_img">
                                <image class="img" mode="widthFix" src="{{item.image_url}}"></image>
                            </view>
                            <view class="loading">
                                <image class="loadind_img" src="./../../image/loading.png"></image>
                                <view class="progress">{{item.progress||''}}</view>
                            </view>
                        </view>
                        <view>
                            <view class="name" wx:if="{{item.title||item.state=='complete'}}">{{item.title||'no title'}}</view>
                            <view class="text text1" wx:else></view>
                            <view class="style" wx:if="{{item.music_metadata.tags}}">{{item.music_metadata.tags}}</view>
                            <view class="text" wx:else></view>
                        </view>
                    </view>
                    <view class="block"></view>
                </view>
            </view>
        </view>
    </scroll-view>
</view>
<view bindtap="onGetTop" class="gotop {{active?'active':''}}">
    <image class="img" mode="widthFix" src="./../../image/gotop.png"></image>
</view>
<page-container closeOnSlideDown="{{false}}" customStyle="{{customStyle}}" duration="{{duration}}" overlay="{{overlay}}" overlayStyle="{{overlayStyle}}" position="{{position}}" round="{{round}}" show="{{dialogShow}}">
    <view class="operate-page" wx:if="{{operateShow}}">
        <view bindtap="onCreate" class="item" data-action="generate">生成类似</view>
        <view bindtap="onCreate" class="item" data-action="continue">从此处继续生成</view>
        <view bindtap="onWhole" class="item" data-action="whole" wx:if="{{songDetail.resultMark==20}}">合成完整歌曲</view>
        <view class="hr"></view>
        <view bindtap="onDelete" class="item">删除</view>
        <view class="hr"></view>
        <view bindtap="onDownloadAudio" class="item">下载音频 <view class="copy">
                <image class="img" mode="widthFix" src="./../../image/icon42.svg"></image> 复制</view>
        </view>
        <view bindtap="onDownloadVideo" class="item">下载视频<view class="copy">
                <image class="img" mode="widthFix" src="./../../image/icon43.svg"></image> 保存</view>
        </view>
    </view>
</page-container>
<mp-dialog extClass="prompt-dialog" show="{{dialogShow2}}" title="请确定是否删除?">
    <view class="deleteimg">
        <button bindtap="onCancelDialog" class="btn cancel" size="mini" type="primary">取消</button>
        <button bindtap="onSureDelete" class="btn" size="mini" type="primary">确定</button>
    </view>
</mp-dialog>
<mp-toptips msg="{{successMsg}}" show="{{successMsg}}" type="success"></mp-toptips>
<mp-toptips msg="{{errorMsg}}" show="{{errorMsg}}" type="error"></mp-toptips>
