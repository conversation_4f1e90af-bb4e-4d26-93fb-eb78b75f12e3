var t = getApp();

Component({
    properties: {
        navbarData: {
            type: Object,
            value: {},
            observer: function(t, i) {}
        }
    },
    data: {
        height: "",
        navbarData: {
            showCapsule: 1,
            showBack: 1,
            showTip: 0,
            showWenhao: 0,
            showHomeTip: 0,
            goHome: 0,
            bg: ""
        },
        tishi_num: 1,
        wenxuan: 2,
        tishi_img: "http://yinyuan.ifree258.top/homeInfo1.png"
    },
    attached: function() {
        this.setData({
            share: t.globalData.share
        }), this.setData({
            barHeight: t.globalData.barHeight,
            barHeightrpx: t.globalData.barHeightrpx,
            lWidth: t.globalData.winWidth - 108,
            windowWidth: t.globalData.winWidth,
            windowHeight: t.globalData.winHeight
        });
    },
    methods: {
        goHome: function() {
            console.log("----nav goHome"), wx.redirectTo({
                url: "/pages/home/<USER>"
            });
        },
        showHomeTip: function() {
            console.log("----show HomeTip"), this.setData({
                tishi_num: 1,
                tishi_img: "http://yinyuan.ifree258.top/homeInfo1.png",
                "navbarData.showHomeTip": !this.data.navbarData.showHomeTip,
                wenxuan: 1
            });
        },
        btn_img: function(t) {
            wx.setStorageSync("knowHomeTip", !0);
            var i = this;
            return 1 == i.data.tishi_num ? (i.setData({
                tishi_img: "http://yinyuan.ifree258.top/homeInfo2.png",
                tishi_num: 2
            }), !1) : 2 == i.data.tishi_num ? (i.setData({
                tishi_img: "http://yinyuan.ifree258.top/homeInfo3.png",
                tishi_num: 3
            }), !1) : 3 == i.data.tishi_num ? (i.setData({
                tishi_img: "http://yinyuan.ifree258.top/homeInfo4.png",
                tishi_num: 4
            }), !1) : 4 == i.data.tishi_num ? (i.setData({
                tishi_img: "http://yinyuan.ifree258.top/homeInfo5.png",
                tishi_num: 5
            }), !1) : 5 == i.data.tishi_num ? (i.setData({
                tishi_img: "http://yinyuan.ifree258.top/homeInfo6.png",
                tishi_num: 6
            }), !1) : void (6 == i.data.tishi_num && (i.setData({
                "navbarData.showHomeTip": !1,
                tishi_num: 1
            }), i.data.wenxuan));
        },
        showTip: function() {
            console.log("---showTip"), wx.showModal({
                title: "是否退出?",
                content: "退出将结束本次寻找，无法再续前缘哦",
                confirmText: "退出",
                success: function(t) {
                    t.confirm && wx.redirectTo({
                        url: "/pages/home/<USER>"
                    });
                }
            });
        },
        _navback: function() {
            console.log("---titlebar back"), wx.navigateBack();
        },
        _backhome: function() {
            wx.switchTab({
                url: "/pages/index/index"
            });
        }
    }
});