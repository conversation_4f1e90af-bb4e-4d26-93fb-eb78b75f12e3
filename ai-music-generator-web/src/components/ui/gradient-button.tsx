"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export interface GradientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  iconSrc?: string;
  label?: string;
  from?: string;
  via?: string;
  to?: string;
}

// TODO optimize style if no from, via, to, use rainbow default
export function GradientButton({
  iconSrc,
  label,
  className,
  from = "var(--primary)",
  via = "var(--primary)",
  to = "rgb(167,112,239)",
  ...props
}: GradientButtonProps) {
  return (
    <button
      type="button"
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-base text-white h-10 group relative rounded-full p-3 md:p-6 transition-all active:brightness-95 hover:shadow-lg",
        className
      )}
      style={{ background: "transparent" }}
      {...props}
    >
      <div
        className="absolute inset-0 rounded-full bg-[length:200%_200%] bg-[50%_50%] transition-all duration-500 ease-in-out group-hover:bg-[100%_100%]"
        style={{
          backgroundImage: `linear-gradient(to bottom right, ${from}, ${via}, ${to})`,
        }}
      />
      <div className="relative z-10 flex items-center justify-center space-x-2">
        {iconSrc ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img src={iconSrc} alt="action" className="h-5 w-6" />
        ) : null}
        {label ? (
          <span className="text-md font-medium text-white">{label}</span>
        ) : null}
      </div>
    </button>
  );
}

export default GradientButton;
