const fs = require('fs');
const path = require('path');

// 读取JSON数据（更可靠）
const jsonData = JSON.parse(fs.readFileSync(path.join(__dirname, '../src/db/init_data/suno.json'), 'utf8'));

// SQL转义函数
const escapeSqlValue = (value) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'boolean') return value;
  if (typeof value === 'number') return value;
  
  const str = String(value);
  return `'${str.replace(/'/g, "''")}'`;
};

// 转换数据格式
const transformedData = jsonData.map(item => {
  const metadata = item.metadata || {};
  
  return {
    uuid: item.id,
    user_uuid: item.user_id || 'default-user-uuid',
    tags: metadata.tags || null,
    prompt: metadata.prompt || null,
    gpt_description_prompt: metadata.gpt_description_prompt || null,
    audio_prompt_id: metadata.audio_prompt_id || null,
    history: metadata.history ? JSON.stringify(metadata.history) : null,
    concat_history: metadata.concat_history ? JSON.stringify(metadata.concat_history) : null,
    type: metadata.type || null,
    duration: metadata.duration || null,
    refund_credits: metadata.refund_credits || null,
    stream: metadata.stream || null,
    error_type: metadata.error_type || null,
    error_message: metadata.error_message || null,
    created_at: item.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
});

// 分批处理数据（每批500条）
const batchSize = 500;
const batches = [];

for (let i = 0; i < transformedData.length; i += batchSize) {
  const batch = transformedData.slice(i, i + batchSize);
  batches.push(batch);
}

// 生成SQL文件
batches.forEach((batch, index) => {
  const values = batch.map(item => {
    const escapedValues = [
      escapeSqlValue(item.uuid),
      escapeSqlValue(item.user_uuid),
      escapeSqlValue(item.tags),
      escapeSqlValue(item.prompt),
      escapeSqlValue(item.gpt_description_prompt),
      escapeSqlValue(item.audio_prompt_id),
      escapeSqlValue(item.history),
      item.concat_history ? `'${item.concat_history.replace(/'/g, "''")}'::jsonb` : 'NULL',
      escapeSqlValue(item.type),
      item.duration || 'NULL',
      item.refund_credits !== null ? item.refund_credits : 'NULL',
      item.stream !== null ? item.stream : 'NULL',
      escapeSqlValue(item.error_type),
      escapeSqlValue(item.error_message),
      escapeSqlValue(item.created_at),
      escapeSqlValue(item.updated_at)
    ];
    
    return `(${escapedValues.join(', ')})`;
  });

  const sql = `INSERT INTO songs (uuid, user_uuid, tags, prompt, gpt_description_prompt, audio_prompt_id, history, concat_history, type, duration, refund_credits, stream, error_type, error_message, created_at, updated_at) VALUES\n${values.join(',\n')};`;
  
  const filename = `insert_songs_batch_${index + 1}.sql`;
  fs.writeFileSync(path.join(__dirname, filename), sql);
  console.log(`Generated ${filename} with ${batch.length} records`);
});

console.log(`Total batches generated: ${batches.length}`);
console.log(`Total records: ${transformedData.length}`);
