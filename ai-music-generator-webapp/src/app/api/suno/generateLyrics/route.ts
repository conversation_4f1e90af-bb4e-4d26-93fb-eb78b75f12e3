import { suno<PERSON>pi } from "@/lib/SunoApi";
import { respData, respErr } from "@/lib/resp";

/**
 * Generate lyrics by suno.
 *
 * @param idea the prompt of generate lyrics
 * @param rankCategory
 * @returns
 */
export async function POST(req: Request) {
  try {
    const body = await req.json();
    // console.log(`---generateLyrics req=`, body);
    const { idea } = body;

    if (!idea) {
      return respErr("Prompt is required");
    }

    const lyrics = await (await sunoApi).generateLyrics(idea);
    // console.log(`---generateLyrics lyrics=`, lyrics);

    return respData(lyrics);
  } catch (e) {
    console.error("generateLyrics error=", e);
    return respErr("generateLyrics");
  }
}
