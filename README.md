# 🎵 AI Music Generator Template

> A ready-to-use AI music generator template (supports WeChat Mini Program and Web App)

![preview](/imgs/preview.png)

## ✨ Demo

> Service maintenance in progress

Search for "音缘" in WeChat Mini Program

Or scan the Mini Program QR code below in WeChat 👇

![yy_mp_code](/imgs/yy_mp_code.png)

### Showcase

https://github.com/user-attachments/assets/9f210ba8-5e4a-4a87-8bd8-519478426081

## 🎯 Core Features

- **AI Music Generation**: Generate high-quality commercial-grade music using Suno AI
- **Convenient Login**: Sign in with WeChat or phone number
- **Secure Payment**: Support for WeChat Pay
- **Business Monetization**: Configurable pricing plans and promotional activities
- **Beautiful Interface**: Smooth and user-friendly music generation and playback experience
- **Practical Features**: Share and forward, download audio, download video
- **One-Click Deployment**: Support for one-click deployment to Vercel

## 🔬 Key Technologies

- WeChat Mini Program Native: wxml, wxss, javascript (similar to HTML, CSS, JS)
- NextJs: TypeScript
- Qiniu Cloud Storage: Store image/audio/video and other resources

## 📀 Quick Start

Clone the repository:

```bash
git clone https://github.com/Kevin-free/ai-music-generator ai-music-generator
```

### WeChat Mini Program

[View detailed WeChat Mini Program setup guide](ai-music-generator-miniapp/README.md)

1. Download WeChat Devetools
2. Open WeChat Developer Tools, select "Local Mini Program Project", then choose "Add Project"
3. Select "Import from Local", then choose the "ai-music-generator-miniapp" folder
4. Click "Import"
5. Click "Compile"
6. Click "Preview"
7. Scan the QR code with WeChat to preview in WeChat

### Web App

[View detailed Web App setup guide](ai-music-generator-webapp/README.md)

1. Navigate to the project directory

```bash
cd ai-music-generator-webapp
```

2. Install project dependencies

```bash
pnpm install
```

3. Copy environment variables configuration file

```bash
cp .env.example .env.development
```

4. Start the development server

```bash
pnpm dev
```
