<view class="music_list {{extClass}}">
    <view bind:tap="selectItem" class="music_list_box" data-index="{{index}}" wx:for="{{list}}" wx:key="id">
        <view class="flex">
            <!-- <view class="flag" wx:if="{{index==0||index==1||index==2}}">
                <view class="flag-box">
                    <image class="img" mode="widthFix" src="./../../image/top-flag-red.png"></image>
                    <text class="text">#{{index+1}}</text>
                </view>
            </view> -->
            <view class="cover">
                <image class="coverimg" mode="widthFix" src="{{item.image_url}}"></image>
            </view>
            <view>
                <view class="tips">
                    <text class="version" wx:if="{{!item.isloading&&item.mvVersion}}">{{item.mvVersion}}</text>
                    <text class="part" wx:if="{{!item.isloading&&item.resultMark==20}}">{{item.resultMarkMsg||'Part2'}}</text>
                    <text class="fullsong" wx:if="{{!item.isloading&&item.resultMark==30}}">Full Song</text>
                </view>
                <view class="name">{{item.title||'no Title'}}</view>
                <view class="style">{{item.music_metadata.tags||'no Style'}}</view>
            </view>
        </view>
        <view class="operate">
            <view class="item">
                <image class="img play_icon" mode="widthFix" src="./../../image/icon05.svg"></image>
                <text>{{item.play_count}}</text>
            </view>
            <view catch:tap="onLike" class="item" data-index="{{index}}">
                <image class="img" mode="widthFix" src="./../../image/icon21.svg" wx:if="{{item.is_liked}}"></image>
                <image class="img" mode="widthFix" src="./../../image/icon39.svg" wx:else></image> {{item.upvote_count}} </view>
            <view catch:tap="onShare" class="item">
                <button class="share-btn" data-item="{{item}}" openType="share">
                    <image class="img" mode="widthFix" src="./../../image/icon03.svg"></image>
                </button>
            </view>
            <view catch:tap="openMore" class="item" data-item="{{item}}">
                <image class="img" mode="widthFix" src="./../../image/icon04.svg"></image>
            </view>
        </view>
    </view>
</view>
