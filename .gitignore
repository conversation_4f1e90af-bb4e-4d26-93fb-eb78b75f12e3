# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# ===== GENERAL =====
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# ===== NODE.JS / NEXT.JS SERVER =====
# dependencies
node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# local env files
.env
.env.local
.env.development
.env.production
.env.test

# vercel
.vercel
.tmp

# typescript
*.tsbuildinfo
next-env.d.ts

# ===== WECHAT MINIPROGRAM =====
# WeChat Developer Tools generated files
miniprogram_npm/
project.private.config.json

# ===== LOGS AND RUNTIME =====
# Log files
logs/
*.log
*.log.*
*.log.gz
exceptions.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ===== CERTIFICATES AND PRIVATE KEYS =====
# SSL certificates and private keys
*.pem
*.key
*.crt
*.cert
private/

# ===== DEPLOYMENT AND BUILD =====
# Docker
.dockerignore

# Deployment scripts (if they contain sensitive info)
# deploy.sh

# Build artifacts
dist/
build/

# ===== DEVELOPMENT TOOLS =====
Makefile
.wrangler
wrangler.toml
supabase/
.*next
.source

# ===== TEMPORARY FILES =====
# Temporary folders
tmp/
temp/
.tmp/

# Cache directories
.cache/
.parcel-cache/