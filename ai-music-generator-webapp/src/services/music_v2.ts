import { uploadToQiniu } from "@/lib/qiniu";
import { Music } from "@/types/music";
import fs from "fs";

export const getMusicsFromFile = async (): Promise<Music[]> => {
  try {
    const dataFile = process.env.MUSICS_DATA_FILE;
    if (!dataFile) {
      return [];
    }

    const data = fs.readFileSync(dataFile, "utf8");
    const jsonData = JSON.parse(data);

    console.log(`---getMusicsFromFile jsonData=${JSON.stringify(jsonData)}`);

    // let musics: Music[] = [];
    // 使用 Promise.all 来处理并发的上传操作
    const musics: Music[] = await Promise.all(
      jsonData.map(async (item: any) => {
        // ktodo upload the resource of `item.video_url`, `item.audio_url`, `item.image_url` to 七牛云qiniu,
        // get return 七牛云qiniu url set to `oss_video_url`, `oss_audio_url`, `oss_image_url`.
        // eg. the `item.image_url`="https://cdn1.suno.ai/image_646274c4-3db3-4b47-9c2a-641aaf5e1754.png",
        // set the `oss_image_url`="https://oss.suno.ifree258.top/image_646274c4-3db3-4b47-9c2a-641aaf5e1754.png"

        // 提取文件名作为七牛云的key并上传，只有当URL存在时
        let oss_video_url = "",
          oss_audio_url = "",
          oss_image_url = "";

        if (item.video_url) {
          const videoKey = item.video_url.split("/").pop();
          if (videoKey) {
            oss_video_url = await uploadToQiniu(item.video_url, videoKey);
          }
        }
        if (item.audio_url) {
          const audioKey = item.audio_url.split("/").pop();
          if (audioKey) {
            oss_audio_url = await uploadToQiniu(item.audio_url, audioKey);
          }
        }
        if (item.image_url) {
          const imageKey = item.image_url.split("/").pop();
          if (imageKey) {
            oss_image_url = await uploadToQiniu(item.image_url, imageKey);
          }
        }

        // console.log(
        //   `---video_url=${item.video_url},audio_url=${item.audio_url},image_url=${item.image_url}`
        // );
        // console.log(
        //   `---oss_video_url=${oss_video_url},oss_audio_url=${oss_audio_url},oss_image_url=${oss_image_url}`
        // );

        musics.push({
          music_id: item.id,
          user_id: item.user_id,
          video_url: item.video_url,
          audio_url: item.audio_url,
          image_url: item.image_url,
          oss_video_url: oss_video_url,
          oss_audio_url: oss_audio_url,
          oss_image_url: oss_image_url,
          image_large_url: item.image_large_url,
          major_model_version: item.major_model_version,
          model_name: item.model_name,
          is_liked: item.is_liked,
          display_name: item.display_name,
          is_trashed: item.is_trashed,
          reaction: item.reaction,
          status: item.status,
          title: item.title,
          play_count: item.play_count,
          upvote_count: item.upvote_count,
          is_public: item.is_public,
          created_at: item.created_at,
          music_metadata: {
            music_id: item.id, // 需要根据实际情况调整
            user_id: item.user_id, // 需要根据实际情况调整
            tags: item.metadata.tags,
            prompt: item.metadata.prompt,
            gpt_description_prompt: item.metadata.gpt_description_prompt,
            audio_prompt_id: item.metadata.audio_prompt_id,
            history: item.metadata.history,
            concat_history: item.metadata.concat_history,
            type: item.metadata.type,
            duration: item.metadata.duration,
            refund_credits: item.metadata.refund_credits,
            stream: item.metadata.stream,
            error_type: item.metadata.error_type,
            error_message: item.metadata.error_message,
            created_at: item.created_at, // 这里假设metadata和音乐创建时间相同
          },
        });
      })
    );
    console.log(`---getMusicsFromFile musics=${JSON.stringify(musics)}`);

    return musics;
  } catch (error) {
    console.error("getMusicsFromFile error=", error);
    return [];
  }
};
