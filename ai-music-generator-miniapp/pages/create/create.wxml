<view class="index-page">
    <image class="bg-image" mode="scaleToFill" src="./../../image/bg.jpg"></image>
    <mp-tipsDialog></mp-tipsDialog>
    <view class="main">
        <view class="input-box">
            <view class="header-flex">
                <view bindtap="toggle" class="item {{switchVal=='10'?'active':''}}" data-val="10">灵感模式</view>
                <view bindtap="toggle" class="item {{switchVal=='20'?'active':''}}" data-val="20">自定义模式</view>
            </view>
            <view class="default-input style-box" wx:if="{{switchVal=='10'}}">
                <view class="custom-switch-box">
                    <view bindtap="toggle2" class="custom-switch noshadow {{switchVal2?'active ':''}}">
                        <text class="custom_left" wx:if="{{switchVal2}}"></text>
                        <view class="switch-btn"></view>
                        <text class="custom_right" wx:if="{{!switchVal2}}"></text>
                    </view>
                    <view>纯音乐</view>
                </view>
                <view class="input-content">
                    <view class="title">灵感描述</view>
                    <view class="textarea-box">
                        <textarea bindblur="bindTextAreaBlur" bindinput="bindTextInput" class="textarea" confirmType="done" maxlength="200" placeholder="在此处输入您的灵感，例如：写一首关于爱情的，中国风的歌曲，要求中文，有古筝和二胡，打击乐。" placeholderStyle="font-size:28rpx" showConfirmBar="{{false}}" value="{{params.idea}}"></textarea>
                    </view>
                </view>
                <view class="example" wx:if="{{exampleList.length>0}}">
                    <text class="title">试一试：</text>
                    <view class="item" wx:for="{{exampleList}}" wx:key="index">
                        <text bindtap="selectExample" class="text" data-prompt="{{item.prompt}}" data-title="{{item.title}}">{{item.title}}</text>
                    </view>
                </view>
                <!-- ktodo add option of model -->
                <!-- ktodo add option of keyword -->
            </view>
            <view class="custom-input" wx:else>
                <view class="custom-switch-box">
                    <view bindtap="toggle3" class="custom-switch noshadow {{switchVal3?'active ':''}}">
                        <text class="custom_left" wx:if="{{switchVal3}}"></text>
                        <view class="switch-btn"></view>
                        <text class="custom_right" wx:if="{{!switchVal3}}"></text>
                    </view>
                    <view>纯音乐</view>
                </view>
                <view class="title">歌曲名称</view>
                <input bindinput="bindTitleInput" class="input" confirmType="done" placeholder="输入歌曲名称" placeholderStyle="font-size:28rpx" showConfirmBar="{{false}}" type="text" value="{{params.title}}"></input>
                <view class="input-content" wx:if="{{!switchVal3}}">
                    <view class="tabs">
                        <view bindtap="tabChange" class="tab {{tabIndex==1?'active':''}}" data-index="1">歌词</view>
                        <view bindtap="tabChange" class="tab {{tabIndex==2?'active':''}}" data-index="2">
                            <image class="icon" mode="widthFix" src="./../../image/icon19.svg"></image>AI生成歌词</view>
                    </view>
                    <view class="textarea-box" wx:if="{{tabIndex==1}}">
                        <textarea bindinput="bindLyricInput" class="lyricinput" confirmType="done" maxlength="800" placeholder="请输入您的歌词" placeholderStyle="font-size:28rpx" showConfirmBar="{{false}}" value="{{params.lyric}}"></textarea>
                    </view>
                    <view class="textarea-box" wx:if="{{tabIndex==2}}">
                        <textarea bindinput="bindLyric2Input" class="lyricinput" confirmType="done" maxlength="800" placeholder="请输入歌词参考信息，点击右下角的按钮生成歌词。" placeholderStyle="font-size:28rpx" showConfirmBar="{{false}}" value="{{params.lyric2}}"></textarea>
                        <view bind:tap="onGenerateLyrics" class="random-btn" wx:if="{{!isGenerateLyricsLoading}}"> 生成歌词 </view>
                        <view class="random-btn" wx:else>
                            <image class="loadind_img" src="./../../image/loading.png"></image>
                        </view>
                    </view>
                </view>
                <view class="input-content style">
                    <view class="title">音乐风格</view>
                    <view class="textarea-box">
                        <textarea bindinput="bindStyleInput" class="textarea" confirmType="done" maxlength="200" placeholder="输入音乐风格" placeholderStyle="font-size:24rpx" showConfirmBar="{{false}}" value="{{params.style}}"></textarea>
                        <view bind:tap="onRandomStyle" class="random-btn">
                            <image class="icon" mode="widthFix" src="./../../image/icon19.svg"></image>随机生成音乐风格 </view>
                    </view>
                </view>
                <view class="title" wx:if="{{continueSong}}">
                    <view class="flex">
                        <text>从此处继续生成</text>
                        <input bindblur="bindContinueAtInput" class="clip_input" type="text" value="{{continueAtValue}}"></input>
                    </view>
                    <view bind:tap="onClear" class="clear-btn">
                        <image class="icon" mode="widthFix" src="./../../image/icon18.svg"></image>
                    </view>
                </view>
                <view class="clip-box" wx:if="{{continueSong}}">
                    <image class="clipimg" mode="widthFix" src="{{continueSong.image_url}}"></image>
                    <view>
                        <view class="name">{{continueSong.title}}</view>
                        <view class="style">{{continueSong.style}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="create-btn">
            <button bind:tap="onSubmit" class="btn" type="default">开始生成</button>
        </view>
        <!-- <view class="create-btn">
          <button bind:tap="onSubmit" class="btn" type="default" disabled="{{disabled}}">开始生成</button>
        </view> -->
    </view>
</view>
<mp-toptips msg="{{errorMsg}}" show="{{errorMsg}}" type="error"></mp-toptips>
