<view class="music_list {{extClass}}">
    <view bind:tap="selectItem" class="music_list_box" data-index="{{index}}" wx:for="{{list}}" wx:key="id">
        <view class="cover">
            <image class="coverimg" mode="widthFix" src="{{item.image_url}}"></image>
        </view>
        <view>
            <view class="tips">
                <text class="version" wx:if="{{!item.isloading&&item.mvVersion}}">{{item.mvVersion}}</text>
                <text class="part" wx:if="{{!item.isloading&&item.resultMark==20}}">{{item.resultMarkMsg||'Part2'}}</text>
                <text class="fullsong" wx:if="{{!item.isloading&&item.resultMark==30}}">Full Song</text>
            </view>
            <view class="name">{{item.title||'no Title'}}</view>
            <view class="style">{{item.music_metadata.tags||'no Style'}}</view>
            <view catch:tap="onShare" class="share">
                <button class="share-btn" data-item="{{item}}" openType="share">
                    <image class="img" mode="widthFix" src="./../../image/icon03.svg"></image>
                </button>
            </view>
            <view catch:tap="openMore" class="more" data-item="{{item}}">
                <image class="img" src="./../../image/icon04.svg"></image>
            </view>
        </view>
    </view>
</view>
