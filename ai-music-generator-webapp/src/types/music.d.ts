export interface MusicMetadata {
  id?: number;
  music_id: string;
  user_id: string;
  tags?: string;
  prompt?: string;
  gpt_description_prompt?: string;
  audio_prompt_id?: string;
  history?: string;
  concat_history?: any; // JSONB类型可以映射为any或更具体的接口
  type?: string;
  duration?: number;
  refund_credits?: boolean;
  stream?: boolean;
  error_type?: string;
  error_message?: string;
  created_at?: string;
}

export interface Music {
  id?: number;
  music_id: string;
  user_id: string;
  video_url?: string;
  audio_url?: string;
  image_url?: string;
  oss_video_url?: string;
  oss_audio_url?: string;
  oss_image_url?: string;
  image_large_url?: string;
  major_model_version?: string;
  model_name?: string;
  is_liked?: boolean;
  display_name?: string;
  is_trashed?: boolean;
  reaction?: string;
  status?: string;
  title?: string;
  play_count?: number;
  upvote_count?: number;
  is_public?: boolean;
  is_retry?: boolean;
  created_at?: string;
  music_metadata: MusicMetadata;
}
