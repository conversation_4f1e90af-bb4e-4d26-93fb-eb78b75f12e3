.music_list {
  color: #fff;
  margin: 10rpx 30rpx 0;
}
.music_list .title {
  font-size: 40rpx;
  padding: 20rpx;
}
.music_list_box {
  background: #282725;
  border-radius: 10rpx;
  margin: 18rpx 0;
  position: relative;
}
.music_list_box .flex {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding: 15rpx;
}
.music_list_box .coverimg {
  border-radius: 10rpx;
  height: 120rpx;
  margin-right: 20rpx;
  vertical-align: middle;
  width: 120rpx;
}
.music_list_box .cover {
  position: relative;
}
.music_list_box .version {
  color: #e2e8f0;
  margin-right: 16rpx;
}
.music_list_box .fullsong,
.music_list_box .part,
.music_list_box .version {
  background: rgba(226, 232, 240, 0.16);
  border-radius: 6rpx;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
}
.music_list_box .fullsong {
  background: #fbd38d;
  color: #000;
}
.music_list_box .like {
  padding: 10px 15px;
  position: absolute;
  right: 80rpx;
  top: 28rpx;
}
.music_list_box .like .img {
  width: 28rpx;
}
.music_list_box .more {
  padding: 10px 15px;
  position: absolute;
  right: 10rpx;
  top: 32rpx;
}
.music_list_box .more .img {
  height: 28rpx;
  width: 28rpx;
}
.music_list_box .part .img {
  height: 12px;
  vertical-align: sub;
  width: 12px;
}
.music_list_box .btn {
  height: 28rpx;
  left: 50%;
  margin: -14px 0 0-13px;
  position: absolute;
  top: 50%;
  width: 28rpx;
}
.music_list_box .tips {
  position: relative;
  top: -2px;
}
.music_list_box .name,
.music_list_box .style {
  font-size: 30rpx;
  max-width: 420rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.music_list_box .style {
  color: hsla(0, 0%, 100%, 0.5);
  font-size: 26rpx;
}
.music_list_box .style .img {
  width: 26rpx;
}
.block {
  height: 130rpx;
}
.myAudio {
  display: none;
}
.user-class .music_list_box .name,
.user-class .music_list_box .style {
  line-height: 18px;
}
.operate {
  align-items: center;
  border-top: 1px solid #343432;
  display: -webkit-flex;
  display: flex;
  font-size: 22rpx;
  gap: 20px;
  justify-content: flex-end;
  padding: 20rpx;
  position: relative;
}
.operate .img {
  margin-right: 10rpx;
  position: relative;
  top: -2rpx;
  vertical-align: middle;
  width: 28rpx;
}
.operate .img.play_icon {
  width: 24rpx;
}
.operate .share-btn {
  background: transparent;
  color: #fff;
  font-size: 24rpx;
  font-weight: 400;
  margin: 0;
  min-width: 20rpx !important;
  padding: 0 !important;
  width: 185rpx !important;
}
