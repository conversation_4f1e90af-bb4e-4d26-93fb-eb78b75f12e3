/** @type {import('next').NextConfig} */
const nextConfig = {
   webpack: (config, { isServer }) => {
     if (isServer) {
       return {
         ...config,
         resolve: {
           ...config.resolve,
           alias: { ...config.resolve.alias, hexoid: "hexoid/dist/index.js" },
         },
         // 引入微信支付相关包后，警告【Critical dependency: the request of a dependency is an expression】关闭
         module: { ...config.module, exprContextCritical: false },
       };
     }
     return config;
   },
 };
 
 export default nextConfig;