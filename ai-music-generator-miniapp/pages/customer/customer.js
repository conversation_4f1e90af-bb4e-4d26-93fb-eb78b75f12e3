const { VERSION } = require('../../utils/constants');

Page({
  data: { subscriber: !1 },
  onLoad: function (t) {
    this.setData({ VERSION: VERSION });
  },
  onReady: function () {},
  onShow: function () {
    var t = wx.getStorageSync('userInfo');
    this.setData({ subscriber: t.subscriber });
  },
  onCopy: function (t) {
    var a = t.currentTarget.dataset.item;
    wx.setClipboardData({
      data: a,
      success: function (t) {
        wx.getClipboardData({
          success: function (t) {
            wx.showToast({ title: '复制成功', icon: 'none' });
          },
        });
      },
    });
  },
});
