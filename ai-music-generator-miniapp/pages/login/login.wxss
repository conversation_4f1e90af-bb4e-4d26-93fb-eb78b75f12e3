.login-page {
  background: #232323;
  color: #fff;
  font-size: 28rpx;
  height: 100vh;
  padding: 40rpx;
  text-align: center;
}
.login-page .logo_img {
  border-radius: 50%;
  height: 80rpx;
  margin: 200rpx 0 10rpx;
  width: 80rpx;
}
.login-page .btn {
  border-radius: 100rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
  padding: 10px 0;
  width: auto;
}
.phone_btn {
  background: #ff3a3a;
  border: 1px solid #ff3a3a;
  color: #fff;
  margin-top: 60rpx;
}
.code_btn {
  background: transparent;
  border: 1px solid #ccc;
  color: #fff;
}
.code_login .input {
  border: 1px solid #aaa8a8;
  border-radius: 100rpx;
  margin-bottom: 30rpx;
  padding: 18rpx 0 18rpx 40rpx;
  text-align: left;
}
.code_login {
  margin-top: 60rpx;
}
.login-page .phone_btn[disabled] {
  background: #9a8257 !important;
  border: 1px solid #9a8257;
  color: #000 !important;
}
.other {
  color: #7c7b7b;
  margin-bottom: 10rpx;
}
.code_input {
  position: relative;
}
.code_input .text {
  color: #fad596;
  font-size: 24rpx;
  position: absolute;
  right: 25rpx;
  top: 25rpx;
  z-index: 999;
}
.login-page .agreement {
  color: #10AEFF;
}
.login-page .wx-checkbox-input {
  border-radius: 50%;
  height: 30rpx;
  vertical-align: sub;
  width: 30rpx;
}
.checkbox {
  display: inline-block;
  position: relative;
  top: -2px;
}
.name {
  line-height: 18px;
}
