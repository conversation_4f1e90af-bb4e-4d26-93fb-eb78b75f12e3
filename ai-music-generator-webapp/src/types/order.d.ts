import { PayType, PayStatus, PeriodType } from "@/types/common";

export interface Order {
  id?: number; // 使用数字作为自增的ID
  orderId: string; // UUID作为字符串类型
  userId: string; // UUID作为字符串类型
  packageId: string; // UUID作为字符串类型
  outTradeNo: string; // 唯一的商户订单号
  transactionId?: string; // 可选的微信订单号
  nonceStr?: string; // 可选的随机字符串
  prepayId?: string; // 可选的支付产生的id
  payType: PayType; // 枚举类型表示支付方式
  payStatus: PayStatus; // 枚举类型表示支付状态
  amount: number; // 订单金额，单位为分
  currency?: string; // 可选的货币类型
  period?: PeriodType; // 可选的周期类型
  credits: number; // 积分
  paiedAt?: string; // 可选的支付时间
  expiredAt?: string | null; // 可选的到期时间
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
}
