import { respData, respErr } from "@/lib/resp";
import { getMusicsByUserId, getUserMusicsCount } from "@/models/music";
import { Music } from "@/types/music";

// 获取我的歌曲列表
export async function POST(req: Request) {
  try {
    // console.log(`---myList req=${JSON.stringify(req.json)}`);

    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }
    const { page, pageSize, from } = await req.json();

    const totalMusicsCount = await getUserMusicsCount(user_id); // 假设此函数返回一个整数
    const pages = Math.ceil(totalMusicsCount / pageSize);

    // 分页查询
    const musics: Music[] = await getMusicsByUserId(
      user_id,
      from,
      page,
      pageSize
    );

    // 包装返回数据
    const wrapResponse = (musics: Music[]): any => {
      const records = musics.map((music) => {
        return {
          id: music.id, // 这需要一个映射，因为原始数据没有这个字段
          // taskId: music.music_id,
          user_id: music.user_id,
          title: music.title,
          mvVersion: music.major_model_version,
          music_id: music.music_id,
          audio_url: music.audio_url,
          video_url: music.video_url,
          image_url: music.image_url,
          oss_audio_url: music.oss_audio_url,
          oss_video_url: music.oss_video_url,
          oss_image_url: music.oss_image_url,
          state: music.status,
          resultMark: 10, // 结果标记。10=默认，20=resultMarkMsg||'Part2'，30=Full Song。
          resultMarkMsg: null,
          createdBy: "system",
          updatedBy: null,
          updationDate: null,
          creationDate: null,
          enabledFlag: 1,
          continueFlag: true,
          music_metadata: {
            music_id: music.music_id,
            tags: music.music_metadata.tags,
            prompt: music.music_metadata.prompt,
            duration: music.music_metadata.duration,
          },
        };
      });

      // 分页处理
      return {
        records: records,
        total: records.length,
        size: pageSize, // 每页数
        current: page, // 当前页
        pages: pages, // 总页数
      };
    };

    // 使用此函数构造响应
    const response = wrapResponse(musics);
    // console.log(`---myList rsp=${JSON.stringify(response)}`);
    return respData(response);
  } catch (e) {
    console.error("myList error=", e);
    return respErr("myList");
  }
}
