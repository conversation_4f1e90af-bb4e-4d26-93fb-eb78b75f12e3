import { respData, respErr } from "@/lib/resp";

import { User } from "@/types/user";
import { config } from "../../../../config/config";
import { myHttpRequest } from "@/lib/http";
import * as utils from "@/lib/utils";
import { saveUser } from "@/services/user";
import { findUserByPhone } from "@/models/user";
import { genUuid } from "@/lib/index";

const wxconfig = config.weapp_auth;
const wxapiconfig = config.weapp_api;

/**
 * login via wechat miniprogram
 * with user phone
 *
 * @param code 用户登录凭证（有效期五分钟）
 * @param phoneCode 动态令牌。可通过动态令牌换取用户手机号
 * @param comeFrom 来源。1010=微信小程序
 * @returns
 */
export async function POST(req: Request) {
  try {
    console.log(`---miniLogin req=${JSON.stringify(req.json)}`);
    const body = await req.json();
    const { code, phoneCode, comeFrom } = body;
    console.log(
      `---code=${code}, phoneCode=${phoneCode}, comeFrom=${comeFrom}`
    );

    if (!code) {
      return respErr("验证错误，缺失的code");
    }

    // 登录凭证校验。通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。
    let authRequest = new myHttpRequest(wxconfig.host, wxconfig.path, "https");
    let weapp: string;
    try {
      weapp = await authRequest.get({
        appid: process.env.APP_ID,
        secret: process.env.APP_SECRET,
        js_code: code,
        grant_type: wxconfig.grant_type,
      });
    } catch (error) {
      return respErr("请求微信服务端失败" + error);
    }

    let results: any = null;
    try {
      results = JSON.parse(weapp);
    } catch (error) {
      return respErr("解析错误" + error);
    }

    console.log(`---weapp=${weapp}, results=${results}`);

    let openid = results.openid;
    let session_key = results.session_key;
    if (openid && session_key) {
      let openid_str = openid.substring(openid.length - 8); // 截取后8位
      let time_str = new Date().getTime().toString().substring(5); // 截取时间戳后五位
      let rds_session_key = `${openid_str}_${utils.randomString(
        6
      )}_${time_str}`;
      console.log(`---openid=${openid}, rds_session_key=${rds_session_key}`);

      // ktodo set session_key into redis?

      // get access_token
      let get_access_token_req = new myHttpRequest(
        wxconfig.host,
        wxapiconfig.get_access_token,
        "https"
      );
      let get_access_token_rsp: string;
      try {
        get_access_token_rsp = await get_access_token_req.get({
          appid: process.env.APP_ID,
          secret: process.env.APP_SECRET,
          grant_type: "client_credential",
        });
      } catch (error) {
        return respErr("请求微信服务端失败" + error);
      }
      let get_access_token_res: any = null;
      try {
        get_access_token_res = JSON.parse(get_access_token_rsp);
      } catch (error) {
        return respErr("解析错误" + error);
      }
      console.log(
        `---get_access_token_rsp=${get_access_token_rsp}, get_access_token_res.access_token=${get_access_token_res.access_token}`
      );

      // get user phone
      // 接口调用凭证，该参数为 URL 参数，非 Body 参数。
      let get_userphone_req = new myHttpRequest(
        wxconfig.host,
        wxapiconfig.getuserphonenumber,
        "https"
      );
      let get_userphone_rsp: string;
      try {
        get_userphone_rsp = await get_userphone_req.postparams(
          { code: phoneCode }, // Body参数
          { access_token: get_access_token_res.access_token } // URL参数
        );
      } catch (error) {
        return respErr("请求微信服务端失败" + error);
      }
      let get_userphone_res: any = null;
      try {
        get_userphone_res = JSON.parse(get_userphone_rsp);
      } catch (error) {
        return respErr("解析错误" + error);
      }
      console.log(`---get_userphone_rsp=${get_userphone_rsp}`);
      let purePhoneNumber = get_userphone_res.phone_info.purePhoneNumber;
      console.log(`---purePhoneNumber=${purePhoneNumber}`);

      // insert into users
      // deal with nickName
      // 生成昵称：替换手机号中间四位为星号
      // if purePhoneNumber = 15712346130, nickName = 157****6130
      let nickName = `${purePhoneNumber.substring(
        0,
        3
      )}****${purePhoneNumber.substring(7)}`;
      // ktodo get deafult value from config
      const userInfo: User = {
        user_id: genUuid(),
        phone: purePhoneNumber,
        email: "",
        nickName: nickName,
        headerUrl:
          "https://common-911.oss-cn-shenzhen.aliyuncs.com/ti-mi.cn/music/header/suno_header.png",
        coin: "",
        maxRunningJob: 1,
        subscriber: false,
        bindEmail: "",
        sensitiveFlag: 0,
        setPassword: "",
        iosPay: false,
      };
      await saveUser(userInfo);

      const existUser = await findUserByPhone(purePhoneNumber);
      if (existUser) {
        let rspData: any = {
          accessToken: rds_session_key,
          userId: existUser.user_id,
        };
        console.log(`---miniLogin rsp=${JSON.stringify(rspData)}`);
        return respData(rspData);
      } else {
        return respErr("user does not exist");
      }
    }
    if (results.errcode || results.errmsg) {
      return respErr("Call wxapi failed.");
    } else {
      // 未知错误处理
      return respErr("Call wxapi failed. Unknow error");
    }
  } catch (error) {
    console.error("miniLogin error=", error);
    return respErr("miniLogin error");
  }
}
