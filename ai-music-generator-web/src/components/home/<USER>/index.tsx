import { Bs<PERSON><PERSON><PERSON>, BsTwitterX } from "react-icons/bs";
import { FaInstagram, FaTik<PERSON> } from "react-icons/fa";

export default function () {
  return (
    <div className="mx-auto flex flex-row items-center justify-center">
      <a
        href="https://twitter.com/aimusic"
        target="_blank"
        rel="nofollow"
        className="mx-3 flex max-w-[24px] flex-col items-center justify-center"
      >
        <BsTwitterX className="text-lg" />
      </a>
      <a
        href="https://www.instagram.com/aimusic"
        target="_blank"
        rel="nofollow"
        className="mx-3 flex max-w-[24px] flex-col items-center justify-center"
      >
        <FaInstagram className="text-lg" />
      </a>
      <a
        href="https://www.tiktok.com/@aimusic"
        target="_blank"
        rel="nofollow"
        className="mx-3 flex max-w-[24px] flex-col items-center justify-center"
      >
        <FaTiktok className="text-lg" />
      </a>
      <a
        href="https://discord.gg/kfbbRha3"
        target="_blank"
        rel="nofollow"
        className="mx-3 flex max-w-[24px] flex-col items-center justify-center"
      >
        <BsDiscord className="text-lg" />
      </a>
    </div>
  );
}
