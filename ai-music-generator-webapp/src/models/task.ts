import { Task } from "@/types/task";
import { getDb } from "./db";
import { AudioInfo } from "@/lib/SunoApi";
import { QueryResultRow } from "pg";
import logger from "@/lib/logger";

export async function updateTask(task_id: string, taskData: Task) {
  const updated_at: string = new Date().toISOString();
  const db = getDb();
  const query = `
  UPDATE tasks
  SET music_ids = $2, clips = $3, batch_size = $4, status = $5, updated_at = $6
  WHERE task_id = $1;
`;
  const params = [
    task_id,
    taskData.music_ids,
    taskData.clips,
    taskData.batch_size,
    taskData.status,
    updated_at,
  ];
  try {
    const result = await db.query(query, params);
    if (result.rowCount === 0) {
      throw new Error(`updateTask ${task_id} not found.`);
    }
    console.log(`Task ${task_id} has been successfully updated.`);
  } catch (error) {
    console.error(`Error updating task ${task_id}:`, error);
    throw error;
  }
}

export async function insertTask(task: Task) {
  const createdAt: string = new Date().toISOString();

  const db = await getDb();
  const res = await (db.query as any)(
    `INSERT INTO tasks 
      (task_id, music_ids, user_id, clips, status, batch_size, created_at) 
      VALUES 
      ($1, $2, $3, $4, $5, $6, $7)
  `,
    [
      task.task_id,
      task.music_ids,
      task.user_id,
      task.clips,
      task.status,
      task.batch_size,
      createdAt,
    ]
  );
  return res;
}

export async function findTaskByTaskId(
  task_id: string
): Promise<Task | undefined> {
  const db = getDb();
  const res = await db.query(`SELECT * FROM tasks WHERE task_id = $1 LIMIT 1`, [
    task_id,
  ]);
  if (res.rowCount === 0) {
    return undefined;
  }
  const row = res.rows[0];
  const task = formatTask(row);
  return task;
}

export async function findTaskByTaskIds(task_ids: string[]): Promise<Task[]> {
  if (task_ids.length === 0) {
    return [];
  }

  const db = getDb();
  const placeholders = task_ids.map((_, index) => `$${index + 1}`).join(", ");
  const query = `SELECT * FROM tasks WHERE task_id IN (${placeholders})`;
  const res = await db.query(query, task_ids);

  if (res.rowCount === 0) {
    return [];
  }
  let tasks: Task[] = [];
  const { rows } = res;
  rows.forEach((row) => {
    const task = formatTask(row);
    tasks.push(task);
  });
  return tasks;
}

export async function getRunningTaskIds(user_id: string): Promise<string[]> {
  const db = getDb();
  const res = await db.query(
    `SELECT task_id FROM tasks WHERE status = $1 AND user_id = $2`,
    ["running", user_id]
  );
  if (res.rowCount === 0) {
    return [];
  }

  const { rows } = res;
  let runningTaskIds: string[] = [];
  rows.forEach((row) => {
    runningTaskIds.push(row.task_id);
  });

  return runningTaskIds;
}

export async function updateTaskWithAudioInfos(
  taskId: string,
  audioInfos: AudioInfo[]
): Promise<void> {
  const db = getDb();
  const hasError = audioInfos.some((info) => info.status === "error"); // 检查是否有错误状态
  const allComplete = audioInfos.every((info) => info.status === "complete");
  const taskStatus = hasError ? "failed" : allComplete ? "complete" : "running"; // 如果有错误则状态为失败，否则按完整性检查
  const query = `UPDATE tasks SET clips = $2, status = $3 WHERE task_id = $1`;
  const clipsData = JSON.stringify(audioInfos);
  await db.query(query, [taskId, clipsData, taskStatus]);
}

export async function updateTaskGenerate(
  taskId: string,
  audioInfos: AudioInfo[],
  music_ids: string[]
): Promise<void> {
  const db = getDb();
  // const hasError = audioInfos.some((info) => info.status === "error"); // 检查是否有错误状态
  // const allComplete = audioInfos.every((info) => info.status === "complete");
  // const taskStatus = hasError ? "failed" : allComplete ? "complete" : "running"; // 如果有错误则状态为失败，否则按完整性检查
  // const query = `UPDATE tasks SET clips = $2, status = $3 WHERE task_id = $1`;
  const clipsData = JSON.stringify(audioInfos);
  let query: string;
  let params: (string | string[])[];
  query = `UPDATE tasks SET clips = $2, music_ids = $3 WHERE task_id = $1`;
  params = [taskId, clipsData, music_ids];
  try {
    logger.info("updateTaskGenerate Executing SQL", { query, params }); // 日志记录执行的SQL和参数
    await db.query(query, params);
  } catch (error) {
    console.error("Error in updateTaskGenerate", { query, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error updateTaskGenerate ${error}`); // 抛出错误，以供上层处理
  }
}

function formatTask(row: QueryResultRow): Task {
  const task: Task = {
    id: row.id,
    task_id: row.task_id,
    music_ids: row.music_ids,
    user_id: row.user_id,
    clips: row.clips,
    status: row.status,
    batch_size: row.batch_size,
    major_model_version: row.major_model_version,
    metadata: row.metadata,
    created_at: row.created_at,
  };

  return task;
}
