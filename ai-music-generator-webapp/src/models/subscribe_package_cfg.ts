import { SubscribePackageCfg } from "@/types/subscribe_package_cfg";
import { QueryResultRow } from "pg";
import { getDb } from "./db";
import logger from "@/lib/logger";

export async function findSubscribePackageCfgById(
  packageId: string
): Promise<SubscribePackageCfg | undefined> {
  const db = getDb();
  const query = `SELECT * FROM subscribe_package_cfgs WHERE package_id = $1 LIMIT 1`;
  const params = [packageId] as any; // SQL 参数
  try {
    const res = await db.query(query, params);
    if (res.rowCount == 0) {
      return undefined;
    }
    const row = res.rows[0];
    const subscribePackageCfgs = formatSubscribePackageCfg(row);
    return subscribePackageCfgs;
  } catch (error) {
    logger.error("Error in getSubscribePackageCfgs", { query, error }); // 日志记录错误信息及相关SQL和参数
    throw new Error(`Error getSubscribePackageCfgs ${error}`); // 抛出错误，以供上层处理
  }
}

export async function getSubscribePackageCfgs(
  environment: string,
  isBuyLimitOnce?: boolean // 新增参数，标识用户是否已购买单次限购
): Promise<SubscribePackageCfg[]> {
  const db = getDb();// 根据 isBuyLimitOnce 条件动态构建 WHERE 子句
  let whereClause = `WHERE package_type = $1`;
  if (isBuyLimitOnce === true) {
    // 如果用户已购买单次限购，则排除 is_limit_once 为 true 的订阅包
    // fixed 当需要查询 NULL 值时，不能用 `is_limit_once <> TRUE`
    whereClause += ` AND is_limit_once IS NOT TRUE`;
  }
  const query = `
  SELECT * FROM subscribe_package_cfgs 
  ${whereClause}
  ORDER BY id ASC`;
  const params = [environment]; // SQL 参数
  try {
    logger.info("Executing SQL", { query, params }); // 日志记录执行的SQL和参数
    const res = await db.query(query, params);
    if (res.rowCount == 0) {
      return [];
    }
    let subscribePackageCfgs: SubscribePackageCfg[] = [];
    const { rows } = res;
    rows.forEach((row) => {
      const subscribePackageCfg = formatSubscribePackageCfg(row);
      subscribePackageCfgs.push(subscribePackageCfg);
    });
    return subscribePackageCfgs;
  } catch (error) {
    logger.error("Error getSubscribePackageCfgs", { query, error });
    throw new Error(`Error getSubscribePackageCfgs: ${error}`);
  }
}

function formatSubscribePackageCfg(row: QueryResultRow): SubscribePackageCfg {
  const subscribePackageCfg: SubscribePackageCfg = {
    packageId: row.package_id,
    packageName: row.package_name,
    packageType: row.package_type,
    packagePrice: row.package_price,
    amount: row.amount,
    currency: row.currency,
    period: row.period,
    credits: row.credits,
    attributes: row.attributes,
    isLimitOnce: row.is_limit_once,
  };
  return subscribePackageCfg;
}
