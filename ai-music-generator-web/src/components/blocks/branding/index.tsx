import { Section as SectionType } from "@/types/blocks/section";

export default function Branding({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  const isSvg = (src: string | undefined) => {
    return src?.toLowerCase().endsWith(".svg") || false;
  };

  const renderItems = () =>
    section.items?.map((item, idx) => {
      if (item.image) {
        return (
          <div key={idx} className="marquee-item">
            <img
              src={item.image.src}
              alt={item.image.alt || item.title}
              className={`marquee-logo ${
                isSvg(item.image.src) ? "svg-logo" : ""
              }`}
            />
          </div>
        );
      }
      return null;
    });

  return (
    <section id={section.name} className="py-16">
      <div className="marquee-container">
        <div className="marquee-wrapper">
          <div className="marquee-track">{renderItems()}</div>
          <div className="marquee-track" aria-hidden="true">
            {renderItems()}
          </div>
        </div>
      </div>
    </section>
  );
}
