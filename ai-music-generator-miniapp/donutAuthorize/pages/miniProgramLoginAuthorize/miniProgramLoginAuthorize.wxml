<view>
    <view class="donut-authorize_area">
        <view class="donut-authorize_info">
            <image class="donut-authorize_app_icon" src="{{appIcon}}"></image>
            <view class="donut-authorize_appname">{{appName}}</view>
            <view>申请</view>
        </view>
        <view class="donut-authorize_content">
            <view class="donut-authorize_item">
                <view class="donut-authorize_icon"></view>
                <view class="donut-authorize_item_content">获取你在当前小程序的登录态信息</view>
            </view>
        </view>
    </view>
    <view class="donut-authorize-watermark_area" wx:if="{{!hasBindOpen}}">
        <view class="donut-authorize-watermark_title">说明</view>
        <view>前往 Donut 平台将移动应用账号绑定于多端应用中，即可消除该水印</view>
    </view>
    <view class="donut-authorize_action">
        <button appParameter="{{dataStr}}" binderror="launchAppError" bindtap="navigateBackToHome" class="donut-authorize_action_confirm donut-authorize_action_btn" openType="launchApp" type="primary" wx:if="{{status===2}}">允许</button>
        <button appParameter="{{dataStr}}" bindtap="loginFailed" class="donut-authorize_action_confirm donut-authorize_action_btn" type="primary" wx:if="{{status===3}}">允许</button>
        <button binderror="launchAppError" bindtap="navigateBackToHome" class="donut-authorize_action_cancel donut-authorize_action_btn" openType="launchApp">拒绝</button>
    </view>
</view>
