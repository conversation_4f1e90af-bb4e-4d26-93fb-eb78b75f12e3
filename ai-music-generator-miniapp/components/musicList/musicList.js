var t = getApp().globalData,
  e = (require('../../services/index'), wx.getBackgroundAudioManager()),
  a = null;
Component({
  properties: {
    musicList: { type: Array },
    extClass: { type: String, value: '' },
  },
  data: { list: [], songDetail: {}, isPlay: !1, playTime: '' },
  observers: {
    musicList: function (t) {
      console.log(t), this.setData({ list: t });
    },
  },
  lifetimes: { attached: function () {} },
  pageLifetimes: { show: function () {} },
  methods: {
    createAudio: function (t) {
      var i = this;
      (e.src = t.audioUrl), (e.title = t.title), (e.coverImgUrl = t.image_url);
      var n = '';
      e.onPlay(function () {
        console.log('开始播放');
        var t = e.duration,
          o = e.currentTime;
        a = setInterval(function () {
          (o = e.currentTime),
            (n = 100 * Number(o / t)),
            console.log(o, t, n),
            i.setData({ playTime: n });
        }, 500);
      }),
        e.onError(function (t) {
          console.log(t.errMsg), console.log(t.errCode);
        }),
        e.onPause(function () {
          console.log('暂停播放'), clearInterval(a);
        });
    },
    audioPlay: function (t) {
      var e = t.currentTarget.dataset.item;
      this.createAudio(e), this.setData({ songDetail: e, isPlay: !0 });
    },
    audioPause: function () {
      e.pause(), clearInterval(a), this.setData({ isPlay: !1 });
    },
    selectItem: function (e) {
      (t.currentIndex = e.currentTarget.dataset.index),
        (t.songlist = this.data.list),
        wx.setStorageSync('songlist', this.data.list),
        wx.navigateTo({ url: '/pages/play/play' });
    },
    openMore: function (t) {
      this.triggerEvent('myCustomEvent', t.currentTarget.dataset.item, {});
    },
  },
});
