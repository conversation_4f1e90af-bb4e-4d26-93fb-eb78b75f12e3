.music_list {
  color: #fff;
  padding-bottom: 40rpx;
}
.music_list .title {
  font-size: 40rpx;
  padding: 20rpx;
}
/* .music_list_box {
  background: linear-gradient(
      0deg,
      hsla(38, 93%, 77%, 0),
      hsla(38, 93%, 77%, 0.2) 200%
    ),
    #272626;
  border-radius: 5px;
  margin: 15px;
  position: relative;
} */

.music_list_box {
  background: #282725;
  border-radius: 5px;
  margin: 18rpx 40rpx;
  position: relative;
}
.music_list_box .flex {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  padding: 15rpx;
}
.music_list_box .coverimg {
  border-radius: 10rpx;
  height: 120rpx;
  margin-right: 20rpx;
  vertical-align: middle;
  width: 120rpx;
}
.music_list_box .cover {
  position: relative;
}
.music_list_box .version {
  color: #e2e8f0;
  margin-right: 16rpx;
}
.music_list_box .fullsong,
.music_list_box .part,
.music_list_box .version {
  background: rgba(226, 232, 240, 0.16);
  border-radius: 6rpx;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
}
.music_list_box .fullsong {
  background: #ff3a3a;
  color: #000;
}
.music_list_box .more {
  padding: 10px 15px;
  position: absolute;
  right: 40rpx;
  top: 32rpx;
}
.music_list_box .more .img {
  height: 28rpx;
  width: 28rpx;
}
.music_list_box .part .img {
  height: 12px;
  vertical-align: sub;
  width: 12px;
}
.music_list_box .btn {
  height: 28rpx;
  left: 50%;
  margin: -14px 0 0-13px;
  position: absolute;
  top: 50%;
  width: 28rpx;
}
.music_list_box .tips {
  position: relative;
  top: -2px;
}
.music_list_box .name,
.music_list_box .style {
  font-size: 28rpx;
  max-width: 460rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.music_list_box .style {
  color: #cbd5e0;
  font-size: 24rpx;
}
.block {
  height: 130rpx;
}
.myAudio {
  display: none;
}
.user-class .music_list_box .name,
.user-class .music_list_box .style {
  line-height: 18px;
}
.operate {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  font-size: 22rpx;
  justify-content: space-between;
  padding: 0 20rpx;
  position: relative;
  border-top: 1px solid #343432;
}
.operate .img {
  margin-right: 10rpx;
  position: relative;
  top: -2rpx;
  vertical-align: middle;
  width: 14px;
}
.operate .img.play_icon {
  width: 12px;
}
/* .music_list .music_list_box:nth-child(1) .operate,
.music_list_box:nth-child(2) .operate,
.music_list_box:nth-child(3) .operate {
  background: linear-gradient(
      0deg,
      hsla(38, 93%, 77%, 0),
      hsla(38, 93%, 77%, 0.2) 150%
    ),
    #272626;
} */
.operate .share-btn {
  background: transparent;
  font-size: 24rpx;
  width: 16px;
}
.flag {
  left: -15rpx;
  position: absolute;
  top: 20rpx;
  z-index: 99;
}
.flag-box {
  position: relative;
}
.flag .img {
  width: 70rpx;
}
.flag .text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 700;
  left: 9px;
  position: absolute;
  top: 9rpx;
  z-index: 999;
}
