.auditing,
.index-page {
  background: #131313;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 60px;
}
.auditing {
  color: #fff;
  padding: 40rpx;
  text-align: center;
}
.box {
  border-radius: 20rpx;
  margin: 30px 0;
  padding: 20px 0;
}
.auditing .logo_img {
  margin-bottom: 10px;
  width: 140rpx;
}
.auditing .text {
  font-size: 48rpx;
}
.auditing .btn {
  background: #ff3a3a !important;
  border-radius: 80rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin: auto;
  padding: 15rpx 0rpx;
  text-align: center;
  width: 50%;
}
.example-scroll {
  border-bottom: 1rpx solid #3b3b3b;
  color: #fff;
  display: -webkit-flex;
  display: flex;
  margin: 0 40rpx 10rpx;
  opacity: 0.8;
  padding: 20rpx 0 0 0rpx;
  white-space: nowrap;
}
.tab-content {
  padding-top: 5rpx;
}
.nav {
  font-size: 30rpx;
  margin-right: 10px;
  padding: 10rpx;
}
.nav.active {
  border-bottom: 1rpx solid #ff3a3a;
  color: #ff3a3a;
}
.nav .img {
  margin-right: 4rpx;
  position: relative;
  top: -2rpx;
  width: 32rpx;
}
.title {
  color: #fff;
  padding: 20rpx 20rpx 0;
}
.operate-page {
  color: #ffffffd5;
  padding: 20rpx 40rpx;
}
.operate-page .item {
  font-size: 28rpx;
  margin: 25rpx 0;
}
.operate-page .hr {
  border-bottom: 1rpx solid hsla(0, 4%, 85%, 0.16);
}
.tips_dialog .weui-dialog {
  border-radius: 10rpx;
  transform: translate(0, -50%);
  width: 90%;
}
.tips_dialog .weui-dialog__title {
  font-weight: 400;
  padding-top: 10px;
}
.tips_dialog .weui-dialog__hd {
  padding: 10rpx !important;
}
.tips_dialog .weui-dialog__bd {
  font-size: 28rpx;
  margin-bottom: 0;
}
.weui-tabs-swiper {
  height: 100vh !important;
}
