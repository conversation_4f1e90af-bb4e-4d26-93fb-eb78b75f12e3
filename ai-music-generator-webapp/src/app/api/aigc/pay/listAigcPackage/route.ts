import logger from "@/lib/logger";
import { respData, respErr } from "@/lib/resp";
import { getSubscribePackageCfgs } from "@/models/subscribe_package_cfg";
import { findUserByUserId } from "@/models/user";

/**
* get list of subscribe package config
*
* @returns list
*/
export async function POST(req: Request) {
  try {
    const api_id = req.headers.get("x-apiid") || "";
    const token = req.headers.get("x-token") || "";
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const body = await req.json(); 
    logger.info(`---listAigcPackage req=`, body); 
    const environment = body.environment
    if (!environment) {
      return respErr("Invalid params");
    }

    const user = await findUserByUserId(user_id);
    if(!user) {
      return respErr("User Empty");
    }

    const data = await getSubscribePackageCfgs(environment, user.isBuyLimitOnce);

    logger.info(`---listAigcPackage rsp=${JSON.stringify(data)}`);
    return respData(data);
  } catch (error) {
    logger.error("listAigcPackage error=", error);
    return respErr("listAigcPackage error");
  }
}
