const { URI } = require('../utils/constants');

var t,
  n = require('../@babel/runtime/helpers/defineProperty'),
  e = require('../utils/util'),
  o = function (t, n, o) {
    var i = URI + t,
      r = wx.getStorageSync('userId'),
      u = (0, e.getToken)();
    return new Promise(function (t, e) {
      wx.request({
        url: i,
        method: n,
        data: o,
        timeout: 180000,        // 设置超时时间为3分钟
        header: {
          'x-token': u,
          'x-user-id': r,
          'x-apiid': '154681495417021010',
        },
        success: function (n) {
          t(n.data);
        },
        fail: function (t) {
          e(t);
        },
        complete: function (t) {},
      });
    });
  };
(Promise.prototype.finally = function (t) {
  var n = this.constructor;
  return this.then(
    function (e) {
      n.resolve(t()).then(function () {
        return e;
      });
    },
    function (e) {
      n.resolve(t()).then(function () {
        throw e;
      });
    },
  );
}),
  (module.exports =
    (n(
      (t = {
        // API_BASE_URL: 'https://suno.g-mi.cn/apiclouds',
        // API_BASE_URL: 'http://localhost:3000/api',
        request: o,
        fastLogin: function (t) {
          return o('/aigc/user/fastLogin', 'post', t);
        },
        miniLogin: function (t) {
          return o('/aigc/user/miniLogin', 'post', t);
        },
        listExplore: function (t) {
          return o('/suno/listExplore', 'post', t);
        },
        myList: function (t) {
          return o('/suno/myList', 'post', t);
        },
        listAigcPackage: function (t) {
          return o('/aigc/pay/listAigcPackage', 'post', t);
        },
        batchQuery: function (t) {
          // return o('/suno/batchQuery', 'post', t);
          return o('/suno/batchQueryV4', 'post', t);
        },
        getRemainCount: function (t) {
          return o('/aigc/user/getRemainCount', 'post', t);
        },
        getUser: function (t) {
          return o('/aigc/user/getUser', 'post', t);
        },
        listRunningTaskIds: function (t) {
          return o('/suno/listRunningTaskIds', 'post', t);
        },
        generate: function (t) {
          return o('/suno/generate', 'post', t);
          // return o('/suno/generateV1', 'post', t);
        },
        generateLyrics: function (t) {
          return o('/suno/generateLyrics', 'post', t);
        },
        concatWhole: function (t) {
          return o('/suno/concatWhole', 'post', t);
        },
        deleteSong: function (t) {
          return o('/suno/delete', 'post', t);
        },
        getMusicByClipId: function (t) {
          return o('/suno/getMusicByClipId', 'post', t);
        },
      }),
      'deleteSong',
      function (t) {
        return o('/suno/delete', 'post', t);
      },
    ),
    n(t, 'getJsApiPayStatus', function (t) {
      return o('/aigc/pay/getJsApiPayStatus', 'post', t);
    }),
    n(t, 'createJsApiPayInfo', function (t) {
      return o('/aigc/pay/createJsApiPayInfo', 'post', t);
    }),
    n(t, 'getVerifyCode', function (t) {
      return o('/aigc/user/getVerifyCode', 'post', t);
    }),
    n(t, 'verifyLogin', function (t) {
      return o('/aigc/user/verifyLogin', 'post', t);
    }),
    n(t, 'noticeList', function (t) {
      return o('/notice/list', 'post', t);
    }),
    n(t, 'noticeClose', function (t) {
      return o('/suno/view', 'post', t);
    }),
    n(t, "switchHome", function (t) {
      return o("/aigc/config/switchHome", "post", t);
    }),
    n(t, "samples", function (t) {
      return o("/aigc/config/samples", "post", t);
    }),
    t));
