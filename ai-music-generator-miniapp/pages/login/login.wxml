<view class="login-page">
    <image class="logo_img" mode="widthFix" src="./../../image/yinyuan_logo.png"></image>
    <view>音缘</view>
    <view wx:if="{{!isCodeLogin}}">
        <button bind:tap="onFastLogin" class="phone_btn btn">微信一键登录</button>
        <!-- <button bindgetphonenumber="bindgetphonenumber" class="phone_btn btn" openType="getPhoneNumber" wx:if="{{isChecked}}">手机号一键登录</button>
        <button bind:tap="onCheck" class="phone_btn btn" wx:else>手机号一键登录</button> -->
        <!-- <button bind:tap="changeLogin" class="code_btn btn">验证码登录</button> -->
    </view>
    <view class="code_login" wx:else>
        <input bindinput="bindPhoneInput" class="input" confirmType="done" placeholder="输入手机号或邮箱" placeholderStyle="font-size:24rpx" showConfirmBar="{{false}}" type="text"></input>
        <view class="code_input">
            <text bind:tap="getCode" class="text" wx:if="{{countdown}}">获取验证码</text>
            <text class="text" wx:else>{{count}}秒后重发</text>
            <input bindinput="bindCodeInput" class="input" confirmType="done" placeholder="输入验证码" placeholderStyle="font-size:24rpx" showConfirmBar="{{false}}" type="text"></input>
        </view>
        <button bind:tap="onPhoneLogin" class="phone_btn btn" disabled="{{disabled}}">登录</button>
        <view bind:tap="changeLogin2" class="other">其他登录方式</view>
    </view>
    <view>
        <checkbox-group bindchange="onAgreeChange" class="checkbox">
            <checkbox checked="{{isChecked}}" value="1"></checkbox>
        </checkbox-group> 我已阅读并同意 <text bind:tap="openAgree" class="agreement">《用户协议》</text>和 <text bind:tap="openPrivacy" class="agreement">《隐私政策》</text>
    </view>
</view>
<mp-toptips msg="{{errorMsg}}" show="{{errorMsg}}" type="error"></mp-toptips>
