import { respData, respErr } from "@/lib/resp";
import logger from "@/lib/logger";

/**
 * 返回示例
 *
 * @param
 * @returns
 */
export async function POST(req: Request) {
  try {
    // const user_id = req.headers.get("x-user-id") || "";
    // if (!user_id) {
    //   return respErr("user_id is required");
    // }
    const samples = await getSamples(); // 调用服务层获取通知列表数据

    // 生成符合前端要求的数据结构
    const responseData = samples.map((sample) => ({
      title: sample.title,
      prompt: sample.prompt,
    }));

    logger.info(`samples rsp=`, { responseData });
    return respData(responseData); // 发送处理后的响应数据
    // return respData(null); // 发送空数据
  } catch (error) {
    console.error("samples error=", error);
    return respErr("samples error");
  }
}

/**
 * 模拟从数据库或其他源获取通知数据
 */
async function getSamples() {
  // 这里使用静态数据模拟数据库调用结果
  return [
    {
      title: "一首感恩妈妈的生日歌曲",
      prompt: "生成一首感恩妈妈的生日歌曲",
    },
    {
      title: "一首表白心爱之人的浪漫歌曲",
      prompt: "生成一首表白心爱之人的浪漫歌曲",
    },
    {
      title: "一首旅行路上的 Free Style BGM",
      prompt: "生成一首旅行路上的 Free Style BGM",
    },
    {
      title: "一首清凉一夏的欢快歌曲",
      prompt: "生成一首清凉一夏的欢快歌曲",
    },
  ];
}
