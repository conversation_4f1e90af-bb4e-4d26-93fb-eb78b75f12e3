var t = require("./../../services/index");
Page({
  data: {
    exploreList: [],
    songDetail: {},
    dialogShow: !1,
    dialogShow2: !1,
    duration: 500,
    position: "bottom",
    round: !0,
    overlay: !0,
    customStyle: "background:#19191a;height:180px",
    overlayStyle: "",
    tabsList: [
      {
        name: "漫游推荐",
        value: "10",
        icon: "./../../image/icon52.svg",
        activeIcon: "./../../image/icon54.svg",
      },
      {
        name: "热歌榜",
        value: "20",
        icon: "./../../image/icon53.svg",
        activeIcon: "./../../image/icon55.svg",
      },
      {
        name: "新歌榜",
        value: "30",
        icon: "./../../image/icon56.svg",
        activeIcon: "./../../image/icon57.svg",
      },
    ],
    tabActive: 0,
    successMsg: "",
    auditing: !1,
  },
  onLoad: function () {
    wx.showShareMenu({
      withShareTicket: !0,
      menus: ["shareAppMessage", "shareTimeline"],
    }),
      this.getListExplore("10");
  },
  onShow: function () {
    var e = this;
    t.switchHome({}).then(function (t) {
      e.setData({ auditing: t.data });
    });
  },
  getListExplore: function (e) {
    var a = this;
    t.listExplore({ from: "mini", rankCategory: e }).then(function (t) {
      0 === t.code && a.setData({ exploreList: t.data });
    });
  },
  onGetTop: function () {
    this.setData({ scrollTop: 0 });
  },
  onTabclick: function (t) {
    var e = t.currentTarget.dataset,
      a = e.val,
      i = e.index;
    this.getListExplore(a), this.setData({ tabActive: i });
  },
  onChildEvent: function (t) {
    this.setData({ dialogShow: !0, songDetail: t.detail });
  },
  onChildEvent2: function (t) {
    wx.shareAppMessage();
  },
  onShareAppMessage: function (t) {
    wx.showShareMenu({ withShareTicket: !0 });
    var e = t.target.dataset.item;
    return {
      title: e.title,
      path: "/pages/play/play?clipId=".concat(e.clipId),
      imageUrl: e.coverUrl,
      success: function (t) {
        console.log(t);
      },
      fail: function () {
        console.log("--- 转发失败 ---", path);
      },
    };
  },
  onCollect: function (e) {
    var a = this;
    if (!wx.getStorageSync("accessToken"))
      return wx.navigateTo({ url: "/pages/login/login" }), !1;
    t.clickCollect({
      exploreId: this.data.songDetail.id,
      id: this.data.songDetail.id,
    }).then(function (t) {
      0 == t.code && a.setData({ successMsg: "收藏成功", dialogShow: !1 });
    });
  },
  onCreate: function (t) {
    wx.switchTab({ url: "/pages/create/create" }),
      this.setData({ dialogShow: !1 }),
      wx.setStorageSync("songDetail", this.data.songDetail),
      wx.setStorageSync("createAction", t.currentTarget.dataset.action);
  },
  onJump: function () {
    wx.switchTab({ url: "/pages/create/create" });
  },
});
