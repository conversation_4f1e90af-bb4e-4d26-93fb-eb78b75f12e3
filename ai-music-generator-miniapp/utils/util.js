var e,
  o = require('../@babel/runtime/helpers/typeof.js');
var n = ((e = require('./polyfill.js')) && e.__esModule ? e : { default: e })
  .default.btoa;
!(function (e) {
  if ('function' != typeof e.TextEncoder) {
    var o = function () {};
    (o.prototype.encode = function (e) {
      var o = unescape(encodeURIComponent(e))
        .split('')
        .map(function (e) {
          return e.charCodeAt(0);
        });
      return 'function' == typeof Uint8Array ? new Uint8Array(o) : o;
    }),
      (e.TextEncoder = o);
  }
})(window || globalThis);
var t = function (e) {
  return (e = e.toString())[1] ? e : '0'.concat(e);
};
module.exports = {
  formatTime: function (e) {
    var o = e.getFullYear(),
      n = e.getMonth() + 1,
      r = e.getDate();
    e.getHours(), e.getMinutes(), e.getSeconds();
    return ''.concat([o, n, r].map(t).join('/'));
  },
  deepClone: function e(n) {
    var t;
    if ('object' === o(n))
      if (Array.isArray(n)) for (var r in ((t = []), n)) t.push(e(n[r]));
      else if (null === n) t = null;
      else if (n.constructor === RegExp) t = n;
      else for (var a in ((t = {}), n)) t[a] = e(n[a]);
    else t = n;
    return t;
  },
  getToken: function () {
    var e = wx.getStorageSync('accessToken');
    if (e) {
      var o =
          e +
          '^' +
          'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (e) {
            var o = (16 * Math.random()) | 0;
            return ('x' == e ? o : (3 & o) | 8).toString(16);
          }),
        t = (function (e, o) {
          for (var n = new Uint8Array(e.length), t = 0; t < e.length; t++)
            n[t] = e[t] ^ o[t % o.length];
          return n;
        })(
          new TextEncoder().encode(o),
          new TextEncoder().encode(
            'ii-ffE03OJ&#RRFO46W$#FDF)*&YHR05DOW0JO#$%||#RFyDLSe223f0089#',
          ),
        );
      return n(String.fromCharCode.apply(null, t));
    }
    return '';
  },
  downloadImg: function (e) {
    wx.showLoading({ title: '下载中...' }),
      console.log(e),
      wx.downloadFile({
        url: e,
        success: function (e) {
          wx.saveVideoToPhotosAlbum({
            filePath: e.tempFilePath,
            success: function (e) {
              wx.hideLoading(),
                wx.showToast({
                  title: '下载成功',
                  icon: 'success',
                  duration: 600,
                }),
                console.log(e);
            },
            fail: function (e) {
              wx.hideLoading(),
                ('saveImageToPhotosAlbum:fail:auth denied' !== e.errMsg &&
                  'saveImageToPhotosAlbum:fail auth deny' !== e.errMsg &&
                  'saveImageToPhotosAlbum:fail authorize no response' !==
                    e.errMsg) ||
                  wx.showModal({
                    title: '提示',
                    content: '请授权保存到相册',
                    showCancel: !1,
                    success: function () {
                      wx.openSetting({
                        success: function (e) {
                          console.log('openres', e),
                            e.authSetting['scope.writePhotosAlbum']
                              ? console.log(
                                  '获取权限成功，再次点击图片即可保存',
                                )
                              : console.log('获取权限失败，无法保存到相册哦~');
                        },
                        fail: function (e) {
                          console.log('failerr', e);
                        },
                      });
                    },
                  });
            },
          });
        },
        fail: function (e) {
          wx.hideLoading(),
          wx.showToast({
            title: '下载失败，请稍后再试',
            icon: 'none',
            duration: 600,
          }), 
          console.log('下载失败，请稍后再试');
        },
      });
  },
  image2Base64: function (e) {
    return new Promise(function (o, n) {
      wx.request({
        url: e,
        responseType: 'arraybuffer',
        success: function (e) {
          var n = wx.arrayBufferToBase64(e.data);
          o((n = 'data:image/jpeg;base64,' + n));
        },
        fail: function (e) {
          n(!1);
        },
      });
    });
  },
  isValidMMSSFormat: function (e) {
    return /^([0-5][0-9])[:：]([0-5][0-9])$/.test(e);
  },
  randomStyle: function () {
    var e = [
        'acoustic',
        'aggressive',
        'anthemic',
        'atmospheric',
        'bouncy',
        'chill',
        'dreamy',
        'electronic',
        'emotional',
        'epic',
        'experimental',
        'futuristic',
        'groovy',
        'heartfelt',
        'infectious',
        'mellow',
        'romantic',
        'smooth',
        'syncopated',
        'uplifting',
      ],
      o = [
        'afrobeat',
        'anime',
        'ballad',
        'bluegrass',
        'blues',
        'classical',
        'country',
        'dancepop',
        'disco',
        'edm',
        'folk',
        'funk',
        'gospel',
        'grunge',
        'grime',
        'hip hop',
        'house',
        'indie',
        'j-pop',
        'jazz',
        'k-pop',
        'kids music',
        'metal',
        'new wave',
        'opera',
        'pop',
        'punk',
        'rap',
        'reggae',
        'rock',
        'rumba',
        'salsa',
        'samba',
        'sertanejo',
        'soul',
        'swing',
        'synthwave',
        'techno',
        'trap',
      ],
      n = Math.floor(Math.random() * e.length),
      t = Math.floor(Math.random() * o.length);
    return e[n] + ' ' + o[t];
  },
  formatSecondsToMMSS: function (e) {
    var o = Number(e).toFixed() - 3,
      n = Math.floor(o / 60),
      t = (o % 60).toFixed(),
      r = t < 10 ? '0' + t : t;
    return ''.concat(n < 10 ? '0' + n : n, ':').concat(r);
  },
};
