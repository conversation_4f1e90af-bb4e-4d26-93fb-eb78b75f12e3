import logger from "@/lib/logger";
import { respData, respErr } from "@/lib/resp";
import { getUserCredits } from "@/services/order";

export async function POST(req: Request) {
  try {
    const api_id = req.headers.get("x-apiid") || "";
    const token = req.headers.get("x-token") || "";
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const user_credits = await getUserCredits(user_id);

    logger.info(`---getRemainCount rsp=${JSON.stringify(user_credits)}`);

    return respData(user_credits);
  } catch (error) {
    console.error("getRemainCount error=", error);
    return respErr("getRemainCount error");
  }
}
