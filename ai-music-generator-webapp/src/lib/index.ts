import { v4 as uuidv4 } from "uuid";
import axios from "axios";
import * as qiniu from "qiniu";
import logger from "./logger";
import { markUrlIsProcessed } from "@/models/url_log";
import { formatDate, prefixInteger, randomString } from "./utils";
import { updateMusicIsRetry } from "@/models/music";

// 确保环境变量已正确加载
if (
  !process.env.QINIU_ACCESS_KEY ||
  !process.env.QINIU_SECRET_KEY ||
  !process.env.QINIU_BUCKET ||
  !process.env.QINIU_DOMAIN ||
  !process.env.QINIU_PATH
) {
  throw new Error("Qiniu configuration environment variables are required");
}

// 配置七牛云的密钥和存储空间信息
const qiniuConfig = {
  accessKey: process.env.QINIU_ACCESS_KEY, // 从环境变量获取访问密钥
  secretKey: process.env.QINIU_SECRET_KEY, // 从环境变量获取密钥
  bucket: process.env.QINIU_BUCKET, // 七牛云存储空间名
  domain: process.env.QINIU_DOMAIN, // 存储空间绑定的域名
  path: process.env.QINIU_PATH, // 七牛云存储路径
};

// 初始化七牛云上传凭证
const config = new qiniu.conf.Config() as any; // 使用类型断言
config.zone = qiniu.zone.Zone_z2; // 设置存储区域
const mac = new qiniu.auth.digest.Mac(
  qiniuConfig.accessKey,
  qiniuConfig.secretKey
);
const options = {
  scope: qiniuConfig.bucket,
};
const putPolicy = new qiniu.rs.PutPolicy(options);
const uploadToken = putPolicy.uploadToken(mac);

// Generate Universally Unique Identifier eg.`123e4567-e89b-12d3-a456-************`
export function genUuid(): string {
  return uuidv4();
}

// 生成一个商户订单号`out_trade_no`
export function getOutTradeNo(userId?: string | number): string {
  const timeStamp = formatDate(new Date()); // Using the new formatDate function to format the current date and time
  let str = "";
  if (userId) {
    str = `${randomString(4).toUpperCase()}${prefixInteger(userId, 7)}`;
  } else {
    str = randomString(11).toUpperCase();
  }

  return `${timeStamp}${str}`;
}

export async function downloadFileV2(url: string): Promise<Buffer> {
  try {
    logger.info(`开始下载文件：${url}`);
    const startTime = Date.now();
    const response = await axios({
      url,
      responseType: "arraybuffer",
      timeout: 120000,
    }); // 120s
    const endTime = Date.now();
    logger.info(`下载文件成功：${url}，耗时：${endTime - startTime}ms`);
    return response.data;
  } catch (error) {
    logger.error(`下载文件失败：${url}`, error);
    throw new Error(`下载文件失败：${url}，错误：${error}`);
  }
}

export async function uploadToQiniuV2(
  fileBuffer: Buffer,
  key: string
): Promise<string> {
  try {
    logger.info(`开始上传文件到七牛云：${key}`);
    const startTime = Date.now();
    const result = await new Promise<string>((resolve, reject) => {
      const formUploader = new qiniu.form_up.FormUploader(config);
      const putExtra = new qiniu.form_up.PutExtra();
      formUploader.put(
        uploadToken,
        key,
        fileBuffer,
        putExtra,
        (err, body, info) => {
          if (err) {
            logger.error(`上传文件失败：${key}`, err);
            reject(new Error(`上传文件失败：${key}`));
          } else if (info.statusCode === 200) {
            resolve(`${qiniuConfig.domain}/${body.key}`);
          } else {
            logger.error(`上传文件失败，HTTP 状态码：${info.statusCode}`);
            reject(new Error(`上传文件失败，HTTP 状态码：${info.statusCode}`));
          }
        }
      );
    });
    const endTime = Date.now();
    logger.info(`文件上传成功：${key}，耗时：${endTime - startTime}ms`);
    return result;
  } catch (error) {
    logger.error(`上传文件到七牛云失败：${key}`, error);
    throw new Error(`上传文件到七牛云失败：${key}，错误：${error}`);
  }
}

// 封装重试逻辑
// url作为参数，用于错误处理
// musicId 用于更新音乐数据
async function retryOperation<T>(
  operation: () => Promise<T>,
  retries: number = 3,
  errorMsg: string,
  url: string,
  musicId: string
): Promise<T> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const result = await operation();
      // logger.info(`成功:`, result);
      return result;
    } catch (error) {
      logger.error(`${errorMsg} 尝试 ${attempt}/${retries} 失败:`, error);
      if (attempt === retries) {
        logger.error(`${errorMsg} 尝试全部失败:`, error);
        await markUrlIsProcessed(url, false); // 标记URL为未处理
        // 标记music.is_retry=true需要重试
        await updateMusicIsRetry(musicId, true);
        throw error;
      }
      // Exponential backoff
      await new Promise((resolve) =>
        setTimeout(resolve, 1000 * Math.pow(2, attempt))
      );
    }
  }
  throw new Error("Max retries reached");
}

// 下载文件
export async function downloadFileV3(
  url: string,
  musicId: string
): Promise<Buffer> {
  // logger.info(`---downloadFileV3 开始下载文件：${url}`);
  return retryOperation<Buffer>(
    async () => {
      const response = await axios.get(url, {
        responseType: "arraybuffer",
        timeout: 300000, // 300s timeout
      });
      return response.data;
    },
    3,
    `下载文件失败: ${url}`,
    url,
    musicId
  );
}

// 上传文件到七牛云
export async function uploadToQiniuV3(
  fileBuffer: Buffer,
  key: string,
  url: string,
  musicId: string
): Promise<string> {
  // logger.info(`---uploadToQiniuV3 开始上传文件到七牛云：${key}`);
  return retryOperation<string>(
    async () => {
      return new Promise<string>((resolve, reject) => {
        const formUploader = new qiniu.form_up.FormUploader(config);
        const putExtra = new qiniu.form_up.PutExtra();
        formUploader.put(
          uploadToken,
          `${qiniuConfig.path}/${key}`,
          fileBuffer,
          putExtra,
          (err, body, info) => {
            if (err) {
              reject(new Error(`上传文件失败：${key}`));
            } else if (info.statusCode === 200) {
              resolve(`${qiniuConfig.domain}/${body.key}`);
            } else {
              reject(
                new Error(`上传文件失败，HTTP 状态码：${info.statusCode}`)
              );
            }
          }
        );
      });
    },
    3,
    `上传文件到七牛云失败: ${key}`,
    url,
    musicId
  );
}
