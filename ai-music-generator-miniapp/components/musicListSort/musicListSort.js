var t = getApp().globalData,
  e = require("../../services/index");
Component({
  properties: {
    musicList: { type: Array },
    extClass: { type: String, value: "" },
  },
  data: { list: [], songDetail: {}, isPlay: !1, playTime: "" },
  observers: {
    musicList: function (t) {
      this.setData({ list: t });
    },
  },
  methods: {
    selectItem: function (e) {
      (t.currentIndex = e.currentTarget.dataset.index),
        (t.songlist = this.data.list),
        wx.setStorageSync("songlist", this.data.list),
        wx.navigateTo({ url: "/pages/play/play?indexpage=1" });
    },
    onVote: function (t) {
      e.viewLikes({
        exploreId: t.currentTarget.dataset.item.id,
        type: 20,
      }).then(function (t) {
        0 == t.code && wx.showToast({ title: "投票成功" });
      });
    },
    openMore: function (t) {
      this.triggerEvent("myCustomEvent", t.currentTarget.dataset.item, {});
    },
  },
});
