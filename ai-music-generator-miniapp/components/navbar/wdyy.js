var t = getApp(), a = void 0;

Component({
    properties: {
        navbarData: {
            type: Object,
            value: {},
            observer: function(t, a) {}
        }
    },
    data: {
        pace: .5,
        interval: 20,
        length: 0,
        offsetLeft: 0,
        windowWidth: 0,
        height: "",
        navbarData: {}
    },
    attached: function() {
        this.setData({
            share: t.globalData.share
        }), this.setData({
            barHeight: t.globalData.barHeight,
            lWidth: t.globalData.winWidth - 98
        });
    },
    ready: function() {
        console.log("dodo---");
    },
    methods: {
        _navback: function() {
            wx.navigateBack();
        },
        _backhome: function() {
            wx.switchTab({
                url: "/pages/index/index"
            });
        },
        infoDetail: function() {
            console.log("infoDetail---"), console.log("artid:", this.data.navbarData.artid), 
            wx.navigateTo({
                url: "/pages/wdyy/artist/index?id=" + this.data.navbarData.artid
            });
        },
        queryViewWidth: function(t) {
            var a = this;
            return new Promise(function(e) {
                var o = a.createSelectorQuery();
                setTimeout(function() {
                    o.select("." + t).boundingClientRect(function(t) {
                        e(t.width);
                    }).exec();
                }, 1e3);
            });
        },
        stopMarquee: function() {
            console.log("stop-------");
            var t = this;
            clearInterval(a), t.setData({
                offsetLeft: 0
            });
        },
        excuseAnimation: function() {
            console.log("exe----");
            var t = this;
            t.data.length > t.data.windowWidth ? (console.log(">>>>>>"), a = setInterval(function() {
                t.data.offsetLeft <= 0 ? t.data.offsetLeft >= -t.data.length ? t.setData({
                    offsetLeft: t.data.offsetLeft - t.data.pace
                }) : t.setData({
                    offsetLeft: t.data.windowWidth
                }) : t.setData({
                    offsetLeft: t.data.offsetLeft - t.data.pace
                });
            }, t.data.interval), console.log("interval:", a)) : (console.log("<<<<<<<"), t.stopMarquee());
        },
        startMarquee: function() {
            var t = this;
            t.stopMarquee();
            var e = (wx.getSystemInfoSync().windowWidth - 20) / 100 * 60;
            t.data.windowWidth = e, t.queryViewWidth("txt-mName").then(function(e) {
                t.data.length = e, console.log(t.data.length + "/" + t.data.windowWidth), t.data.length > t.data.windowWidth ? (console.log("onshow start >>>>"), 
                t.excuseAnimation()) : (console.log("onshow start <<<<"), clearInterval(a));
            });
        }
    }
});