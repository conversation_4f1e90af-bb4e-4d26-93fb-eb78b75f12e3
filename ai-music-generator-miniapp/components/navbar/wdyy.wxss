.iconfont {
    font-family: "iconfont"!important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
    content: "\e637";
}

.icon-info:after {
    content: "\e643";
}

.nav-wrap {
    position: fixed;
    width: 100%;
    height: 32px;
    padding: 15rpx 0;
    top: 0;
    z-index: 8;
}

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    width: 550rpx;
    height: 32px;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #e5e5e5;
}

.back-box {
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-box .iconfont {
    font-size: 38rpx;
    color: #fff;
}

.back-pre,.back-home {
    width: 50rpx;
    height: 50rpx;
}

.nav-capsule .back-home {
    width: 36rpx;
    height: 40rpx;
    margin-top: 3rpx;
}

.song_box {
    width: 100%;
    height: 32px;
    padding: 15rpx 0;
    display: flex;
    flex-direction: column;
}

.name_txt {
    color: #fff;
    display: flex;
    align-items: center;
    font-size: 34rpx;
    margin-bottom: 15rpx;
}

.author_txt {
    color: #c9c9c9;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    margin-bottom: 5rpx;
}

.musicInfo {
    width: 75%;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

.rollCon {
    width: 100%;
    height: 60rpx;
    font-size: 20rpx;
}

.song-box {
    width: 100%;
    position: relative;
}

.txt-mName {
    white-space: nowrap;
    position: absolute;
    top: 0;
    font-size: 32rpx;
    color: #fff;
}

.txt-mAuthor {
    font-size: 26rpx;
    color: #ccc;
}