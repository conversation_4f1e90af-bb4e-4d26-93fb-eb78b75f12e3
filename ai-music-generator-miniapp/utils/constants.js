// constants.js

const ENV = "prod"; // dev test prod
const VERSION = ENV + "1.5.2"
const CONFIG = {
  dev: "http://localhost:1651/api",
  test: "https://suno.ifree258.top/api",
  prod: "https://suno.ifree258.top/api"
};
const URI = CONFIG[ENV]

// 支付方式
const PayType = Object.freeze({
  WxPay: 0,
  Alipay: 1,
});

// 支付状态
const PayStatus = Object.freeze({
  Created: 0,
  Success: 1,
  Fail: 2,
  Cancel: 3,
});

// 订阅周期类型
const PeriodType = Object.freeze({
  Once: "once",
  Monthly: "monthly",
});

module.exports = {
  ENV,
  VERSION,
  URI,
  PayType,
  PayStatus,
  PeriodType
};
