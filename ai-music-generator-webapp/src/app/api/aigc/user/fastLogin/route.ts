import { respData, respErr } from "@/lib/resp";

import { User } from "@/types/user";
import { config } from "../../../../config/config";
import { myHttpRequest } from "@/lib/http";
import * as utils from "@/lib/utils";
import { findUserByOpenId, saveUserAndAuth } from "@/services/user";
import { genUuid } from "@/lib/index";
import { UserAuth } from "@/types/user_auth";

const wxconfig = config.weapp_auth;

/**
 * fast login via wechat miniprogram
 * without user data
 *
 * @param code 用户登录凭证（有效期五分钟）
 * @returns
 */
export async function POST(req: Request) {
  try {
    console.log(`---fastLogin req=${JSON.stringify(req.json)}`);
    const body = await req.json();
    const { code } = body;
    console.log(`---code=${code}`);

    if (!code) {
      return respErr("验证错误，缺失的code");
    }

    // 登录凭证校验。通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。
    let authRequest = new myHttpRequest(wxconfig.host, wxconfig.path, "https");
    let weapp: string;
    try {
      weapp = await authRequest.get({
        appid: process.env.APP_ID,
        secret: process.env.APP_SECRET,
        js_code: code,
        grant_type: wxconfig.grant_type,
      });
    } catch (error) {
      return respErr("请求微信服务端失败" + error);
    }

    let results: any = null;
    try {
      results = JSON.parse(weapp);
    } catch (error) {
      return respErr("解析错误" + error);
    }

    console.log(`---weapp=${weapp}, results=${results}`);

    let openid = results.openid;
    let session_key = results.session_key;
    if (openid && session_key) {
      let openid_str = openid.substring(openid.length - 8); // 截取后8位
      let time_str = new Date().getTime().toString().substring(5); // 截取时间戳后五位
      let rds_session_key = `${openid_str}_${utils.randomString(
        6
      )}_${time_str}`;
      console.log(`---openid=${openid}, rds_session_key=${rds_session_key}`);

      const userId = genUuid();
      console.log(`---userId=${userId}`);

      // insert into users
      // deal with nickName
      // 生成默认昵称: uuid_userId前8位
      // if userId = "bad443ce-b592-4dad-a91a-5423ae4496e7", nickName = "uuid_bad443ce"
      // 生成默认昵称
      const nickName = `uuid_${userId.substring(0, 8)}`;
      // ktodo get deafult value from config
      const user: User = {
        user_id: userId,
        phone: "",
        email: "",
        nickName: nickName,
        headerUrl:
          "https://common-911.oss-cn-shenzhen.aliyuncs.com/ti-mi.cn/music/header/suno_header.png",
        coin: "",
        maxRunningJob: 1,
        subscriber: false,
        bindEmail: "",
        sensitiveFlag: 0,
        setPassword: "",
        iosPay: false,
      };
      // await saveUser(userInfo);

      const userAuth: UserAuth = {
        user_id: userId,
        auth_type: "wechat mini",
        open_id: openid,
      };

      await saveUserAndAuth(user, userAuth);

      const existUser = await findUserByOpenId(openid);
      if (existUser) {
        let rspData: any = {
          accessToken: rds_session_key,
          userId: existUser.user_id,
        };
        console.log(`---fastLogin rsp=${JSON.stringify(rspData)}`);
        return respData(rspData);
      } else {
        return respErr("user does not exist");
      }
    }
    if (results.errcode || results.errmsg) {
      return respErr("Call wxapi failed." + results.errmsg);
    } else {
      // 未知错误处理
      return respErr("Call wxapi failed. Unknow error");
    }
  } catch (error) {
    console.error("fastLogin error=", error);
    return respErr("fastLogin error");
  }
}
