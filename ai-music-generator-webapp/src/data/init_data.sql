----------------------------------------
-------------   开发版   ----------------
----------------------------------------

-- 安装 uuid-ossp 扩展调用 uuid_generate_v4
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 插入开发版数据到 subscribe_package_cfgs 表
INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：dev test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes,        -- 属性：文本数组
    is_limit_once      -- 是否单次限购
) VALUES (
    uuid_generate_v4(), -- 使用 uuid_generate_v4() 自动生成 UUID
    '限购会员-开发版',          -- 示例名称
    'dev',       -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'once',     -- 每月订阅
    100,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：9.9元，开发者优惠价：0.01元！限购一次</span>', '积分有效期：永久', '100积分（20首歌曲）低至0.49元/首', '作品可商用'],  -- 属性列表
    TRUE
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：dev test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(), -- 使用 uuid_generate_v4() 自动生成 UUID
    '普通会员-开发版',          -- 示例名称
    'dev',       -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    100,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：9.9元，开发者优惠价：0.01元</span>', '积分有效期：1个月', '100积分（20首歌曲）低至0.49元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '白金会员-开发版',           -- 示例名称
    'dev',        -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    500,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：39.9元，开发者优惠价：0.01元</span>', '积分有效期：1个月', '500积分（100首歌曲）低至0.39元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '黄金会员-开发版',           -- 示例名称
    'dev',        -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'quarterly',     -- 每月订阅
    1000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：69.9元，开发者优惠价：0.01元</span>', '积分有效期：3个月', '1000积分（200首歌曲）低至0.34元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '钻石会员-开发版',           -- 示例名称
    'dev',        -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'yearly',     -- 每月订阅
    2000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：119.9元，开发者优惠价：0.01元</span>', '积分有效期：12个月', '2000积分（400首歌曲）低至0.29元/首', '作品可商用']  -- 属性列表
);

----------------------------------------
-------------   体验版   ----------------
----------------------------------------

-- 插入体验版数据到 subscribe_package_cfgs 表
INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：dev test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes,        -- 属性：文本数组
    is_limit_once      -- 是否单次限购
) VALUES (
    uuid_generate_v4(), -- 使用 uuid_generate_v4() 自动生成 UUID
    '限购会员-体验版',          -- 示例名称
    'test',       -- 示例类型
    '¥0.01',       -- 展示用价格
    1,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'once',     -- 每月订阅
    100,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：9.9元，体验官优惠价：0.01元！限购一次</span>', '积分有效期：永久', '100积分（20首歌曲）低至0.49元/首', '作品可商用'],  -- 属性列表
    TRUE
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(), -- 使用 uuid_generate_v4() 自动生成 UUID
    '普通会员-体验版',          -- 示例名称
    'test',       -- 示例类型
    '¥8.8',       -- 展示用价格
    880,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    100,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：9.9元，体验官优惠价：8.8元</span>', '积分有效期：1个月', '100积分（20首歌曲）低至0.49元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '白金会员-体验版',           -- 示例名称
    'test',        -- 示例类型
    '¥35.9',       -- 展示用价格
    3590,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    500,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：39.9元，体验官优惠价：35.9元</span>', '积分有效期：1个月', '500积分（100首歌曲）低至0.39元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '黄金会员-体验版',           -- 示例名称
    'test',        -- 示例类型
    '¥61.9',       -- 展示用价格
    6190,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'quarterly',     -- 每月订阅
    1000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：69.9元，体验官优惠价：61.9元</span>', '积分有效期：3个月', '1000积分（200首歌曲）低至0.34元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '钻石会员-体验版',           -- 示例名称
    'test',        -- 示例类型
    '¥109.9',       -- 展示用价格
    10990,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'yearly',     -- 每月订阅
    2000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：119.9元，体验官优惠价：109.9元</span>', '积分有效期：12个月', '2000积分（400首歌曲）低至0.29元/首', '作品可商用']  -- 属性列表
);

----------------------------------------
-------------   正式版   ----------------
----------------------------------------

-- 插入正式版数据到 subscribe_package_cfgs 表
INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(), -- 使用 uuid_generate_v4() 自动生成 UUID
    '普通会员',          -- 示例名称
    'prod',       -- 示例类型
    '¥9.9',       -- 展示用价格
    990,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    100,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：19.9元，开服优惠价：9.9元</span>', '积分有效期：1个月', '100积分（20首歌曲）低至0.49元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '白金会员',           -- 示例名称
    'prod',        -- 示例类型
    '¥39.9',       -- 展示用价格
    3990,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'monthly',     -- 每月订阅
    500,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：59.9元，开服优惠价：39.9元</span>', '积分有效期：1个月', '500积分（100首歌曲）低至0.39元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '黄金会员',           -- 示例名称
    'prod',        -- 示例类型
    '¥69.9',       -- 展示用价格
    6990,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'quarterly',     -- 每月订阅
    1000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：99.9元，开服优惠价：69.9元</span>', '积分有效期：3个月', '1000积分（200首歌曲）低至0.34元/首', '作品可商用']  -- 属性列表
);

INSERT INTO subscribe_package_cfgs (
    package_id,        -- 订阅包ID：必须是有效的UUID格式
    package_name,      -- 订阅包名称：不超过20字符
    package_type,      -- 订阅包类型：test prod
    package_price,     -- 订阅包展示价格：不超过20字符
    amount,            -- 订阅包实际价格，单位为分
    currency,          -- 货币类型：例如 CNY, USD
    period,            -- 周期类型：例如 once, monthly
    credits,           -- 积分：整数
    attributes         -- 属性：文本数组
) VALUES (
    uuid_generate_v4(),  -- 使用 uuid_generate_v4() 自动生成 UUID
    '钻石会员',           -- 示例名称
    'prod',        -- 示例类型
    '¥119.9',       -- 展示用价格
    11990,         -- 实际价格，单位为分，29900分即299元
    'CNY',         -- 人民币
    'yearly',     -- 每月订阅
    2000,           -- 积分值
    ARRAY['<span style=''color:#FE0000;''>原价：159.9元，开服优惠价：119.9元</span>', '积分有效期：12个月', '2000积分（400首歌曲）低至0.29元/首', '作品可商用']  -- 属性列表
);
