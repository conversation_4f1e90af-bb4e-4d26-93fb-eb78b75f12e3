<mp-notice></mp-notice>
<view class="auditing" wx:if="{{auditing}}">
    <view class="box">
        <image class="logo_img" mode="widthFix" src="./../../image/yinyuan_logo.png"></image>
        <view class="text">音缘AI音乐</view>
    </view>
    <view bind:tap="onJump" class="btn">开始创作</view>
</view>
<view class="index-page" wx:else>
    <scroll-view scrollX class="" enhanced="{{true}}" showScrollbar="{{false}}">
        <view class="example-scroll">
            <view bind:tap="onTabclick" class="nav {{tabActive==index?'active':''}}" data-index="{{index}}" data-val="{{item.value}}" wx:for="{{tabsList}}" wx:key="index"> {{item.name}} </view>
        </view>
    </scroll-view>
    <view class="tab-content">
        <mp-indexMusicList bind:myCustomEvent="onChildEvent" bind:myCustomEvent2="onChildEvent2" musicList="{{exploreList}}" wx:if="{{tabActive==0}}"></mp-indexMusicList>
        <!-- <mp-musicListSort bind:myCustomEvent="onChildEvent" bind:myCustomEvent2="onChildEvent2" musicList="{{exploreList}}" wx:if="{{tabActive==3}}"></mp-musicListSort>
        <mp-musicListMenu bind:myCustomEvent="onChildEvent" bind:myCustomEvent2="onChildEvent2" musicList="{{exploreList}}" wx:if="{{tabActive==1||tabActive==2}}"></mp-musicListMenu> -->
        <mp-indexMusicList bind:myCustomEvent="onChildEvent" bind:myCustomEvent2="onChildEvent2" musicList="{{exploreList}}" wx:if="{{tabActive==3}}"></mp-indexMusicList>
        <mp-indexMusicList bind:myCustomEvent="onChildEvent" bind:myCustomEvent2="onChildEvent2" musicList="{{exploreList}}" wx:if="{{tabActive==1||tabActive==2}}"></mp-indexMusicList>
    </view>
</view>
<page-container closeOnSlideDown="{{false}}" customStyle="{{customStyle}}" duration="{{duration}}" overlay="{{overlay}}" overlayStyle="{{overlayStyle}}" position="{{position}}" round="{{round}}" show="{{dialogShow}}">
    <view class="operate-page">
        <view bindtap="onCreate" class="item" data-action="generate">生成类似</view>
        <view bindtap="onCreate" class="item" data-action="continue">从此处继续生成</view>
        <view bindtap="onCollect" class="item" data-index="{{index}}" data-item="{{item}}">收藏</view>
    </view>
</page-container>
<mp-toptips msg="{{successMsg}}" show="{{successMsg}}" type="success"></mp-toptips>
