import { PeriodType } from "@/types/common";

export interface SubscribePackageCfg {
  id?: number; // 主键，自动生成的序列号
  packageId: string; // 包ID，UUID 格式
  packageName: string; // 订阅包名称，最多 20 个字符
  packageType?: string; // 订阅包类型，可选，最多 10 个字符
  packagePrice: string; // 订阅包价格（展示用），最多 10 个字符
  amount: number; // 订阅包价格（计算用）
  currency: string;
  period: PeriodType;
  credits: number;
  attributes: string[]; // 介绍文本列表，使用数组存储多个属性
  isLimitOnce?: boolean; // 是否单次限购
}
