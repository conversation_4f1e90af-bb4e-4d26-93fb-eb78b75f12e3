import { getMusicIds, insertMusic } from "@/models/music";
import { getMusicsFromFile } from "@/services/music";

// 导入文件更新音乐数据
export async function POST() {
  try {
    const musicIds = await getMusicIds();
    // get music data from json file
    const musics = await getMusicsFromFile();
    const musicsCount = musics.length;
    console.log(
      `musicsCount: ${musicsCount}, exist music count: ${musicIds.length}`
    );
    // musicsCount:3465

    let existCount = 0;
    let newCount = 0;
    let failedCount = 0;
    for (let i = 0; i < musicsCount; i++) {
      const music = musics[i];
      if (!music.music_id) {
        continue;
      }

      if (musicIds && musicIds.includes(music.music_id)) {
        console.log("music exist: ", music.music_id, music.title);
        existCount += 1;
        continue;
      }

      try {
        // insert into musics and music_metadatas
        await insertMusic(music);
        newCount += 1;
        console.log(
          "insert new music: ",
          music.music_id,
          music.title,
          i,
          musicsCount - i
        );
      } catch (e) {
        failedCount += 1;
        console.log("insert music failed: ", music.music_id, music.title, i, e);
      }
    }

    return Response.json({
      data: {
        all_count: musicsCount,
        exist_count: existCount,
        new_count: newCount,
        failed_count: failedCount,
      },
    });
  } catch (error) {
    console.error("update-musics error=", error);
    return Response.json({ error: error });
  }
}
