.notice-container {
  animation: scroll-left 30s linear infinite;
  color: #fff;
  position: fixed;
  top: 0;
  white-space: nowrap;
  z-index: 999;
}
.notice,
.notice-container {
  background: #282725;
}
.notice-container:hover .scroll-item {
  animation-play-state: paused;
}
.notice-text {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  gap: 20rpx;
}
.img {
  height: 40rpx;
  width: 40rpx;
}
.text {
  font-size: 28rpx;
}
.height {
  background: #131313;
  height: 20rpx;
}
@-webkit-keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
