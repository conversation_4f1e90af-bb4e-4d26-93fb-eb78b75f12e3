// `.d.ts`是用来给js格式的内容添加类型辅助声明的，本身只放类型声明相关的东西，并且打包编译的时候不会被编译到包内。
// 所以 enum 需要另外放在 `.ts` 文件中，否则报错 `Module not found`

// 支付方式 0=wxpay 1=alipay
export enum PayType {
  WxPay = 0,
  Alipay = 1,
}

// 支付状态 0=创建 1=成功 2=失败 3=取消
export enum PayStatus {
  Created = 0,
  Success = 1,
  Fail = 2,
  Cancel = 3,
}

// Monthly, quarterly, yearly
export enum PeriodType {
  Once = "once", // 一次性，订阅后可买
  Monthly = "monthly", // 月度1个月
  Quarterly = "quarterly", // 季度3个月
  Yearly = "yearly", // 年度12个月
}
