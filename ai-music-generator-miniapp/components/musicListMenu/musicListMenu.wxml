<view class="music_list {{extClass}}">
    <view bind:tap="selectItem" class="music_list_box" data-index="{{index}}" wx:for="{{list}}" wx:key="id">
        <view class="flex">
            <view class="cover">
                <image class="coverimg" mode="widthFix" src="{{item.coverUrl}}"></image>
            </view>
            <view>
                <view class="tips">
                    <text class="version" wx:if="{{!item.isloading&&item.mvVersion}}">{{item.mvVersion}}</text>
                    <text class="part" wx:if="{{!item.isloading&&item.resultMark==20}}">{{item.resultMarkMsg||'Part2'}}</text>
                    <text class="fullsong" wx:if="{{!item.isloading&&item.resultMark==30}}">Full Song</text>
                </view>
                <view class="name">{{item.title||'no Title'}}</view>
                <view class="style" wx:if="{{item.author}}">
                    <image class="img" mode="widthFix" src="./../../image/icon63.svg"></image> {{item.author}}</view>
            </view>
        </view>
        <view class="operate">
            <view catch:tap="onCollect" class="item" data-index="{{index}}" data-item="{{item}}">
                <image class="img" mode="widthFix" src="./../../image/icon49.svg" wx:if="{{!item.isCollect}}"></image>
                <image class="img" mode="widthFix" src="./../../image/icon50.svg" wx:if="{{item.isCollect}}"></image>
                <text>收藏</text>
            </view>
            <view catch:tap="onShare" class="item share">
                <button class="share-btn" data-item="{{item}}" openType="share">
                    <image class="img" mode="widthFix" src="./../../image/icon03.svg"></image>
                    <text>分享</text>
                </button>
            </view>
            <view catch:tap="openMore" class="item" data-item="{{item}}">
                <image class="img" mode="widthFix" src="./../../image/icon04.svg"></image>
            </view>
        </view>
    </view>
</view>
