import { Order } from "@/types/order";
import { PayType, PayStatus, PeriodType } from "@/types/common";
import { UserCredits } from "@/types/user";
import { getUserMusicsCount } from "@/models/music";
import { getUserOrders } from "@/models/order";

export async function getUserCredits(user_id: string): Promise<UserCredits> {
  // get from .env
  const freeCoin = Number(process.env.FREE_COIN); // 免费金币
  const onceUsedCoin = Number(process.env.ONCE_USED_COIN); // 一次消费金币
  // const freeCoin = 30; // 免费金币
  // const onceUsedCoin = 5; // 一次消费金币
  let user_credits: UserCredits = {
    freeCoin: freeCoin,
    monthCoin: 0,
    totalCoin: freeCoin,
    usedCoin: 0,
    leftCoin: freeCoin,
    concurrentJob: 1,
  };

  try {
    // suno 生成一首歌曲消费5积分
    const used_count = await getUserMusicsCount(user_id);
    user_credits.usedCoin = used_count * onceUsedCoin;

    // console.log(`used_count=${used_count}`);
    const orders = await getUserOrders(user_id);

    if (orders) {
      console.log(`orders=${JSON.stringify(orders)}`);
      orders.forEach((order: Order) => {
        // if (order.period === PeriodType.Monthly) {
        //   user_credits.monthCoin += order.credits;
        // } else {
        //   user_credits.freeCoin += order.credits;
        // }
        if (order.period === PeriodType.Once) {
          user_credits.freeCoin += order.credits;
        } else {
          user_credits.monthCoin += order.credits;
        }
        user_credits.totalCoin += order.credits;
        if (order.expiredAt) {
          user_credits.monthCoinDate = order.expiredAt;
        }
      });
    }

    user_credits.leftCoin = user_credits.totalCoin - user_credits.usedCoin;
    if (user_credits.leftCoin < 0) {
      user_credits.leftCoin = 0;
    }
    // console.log(
    //   `totalCoin=${user_credits.totalCoin}, usedCoin=${user_credits.usedCoin}, leftCoin=${user_credits.leftCoin}`
    // );

    return user_credits;
  } catch (e) {
    console.log("get user credits error=", e);
    return user_credits;
  }
}
