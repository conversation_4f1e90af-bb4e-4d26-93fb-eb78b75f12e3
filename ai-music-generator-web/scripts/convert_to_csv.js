const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");

// 读取并解析原始CSV文件
const results = [];

// 创建一个Promise来处理异步CSV读取
const readCsvFile = () => {
  return new Promise((resolve, reject) => {
    fs.createReadStream(path.join(__dirname, "../src/db/init_data/suno.csv"))
      .pipe(csv())
      .on("data", (data) => results.push(data))
      .on("end", () => resolve(results))
      .on("error", reject);
  });
};

// CSV转义函数
const escapeCsvValue = (value) => {
  if (value === null || value === undefined || value === "") return "";
  const str = String(value);
  if (
    str.includes(",") ||
    str.includes('"') ||
    str.includes("\n") ||
    str.includes("\r")
  ) {
    return `"${str.replace(/"/g, '""')}"`;
  }
  return str;
};

// 重建concat_history JSON
const buildConcatHistory = (row) => {
  const concatHistory = [];
  let index = 0;

  while (row[`metadata/concat_history/${index}/id`]) {
    const item = {
      id: row[`metadata/concat_history/${index}/id`],
      continue_at: row[`metadata/concat_history/${index}/continue_at`]
        ? parseFloat(row[`metadata/concat_history/${index}/continue_at`])
        : null,
    };
    concatHistory.push(item);
    index++;
  }

  return concatHistory.length > 0 ? JSON.stringify(concatHistory) : null;
};

// 主处理函数
const processData = async () => {
  try {
    await readCsvFile();

    // 转换数据格式
    const transformedData = results.map((row) => {
      return {
        uuid: row.id || "",
        user_uuid: row.user_id || "default-user-uuid",
        tags: row["metadata/tags"] || null,
        prompt: row["metadata/prompt"] || null,
        gpt_description_prompt: row["metadata/gpt_description_prompt"] || null,
        audio_prompt_id: row["metadata/audio_prompt_id"] || null,
        history: row["metadata/history/0"] || null,
        concat_history: buildConcatHistory(row),
        type: row["metadata/type"] || null,
        duration: row["metadata/duration"]
          ? parseFloat(row["metadata/duration"])
          : null,
        refund_credits:
          row["metadata/refund_credits"] === "true"
            ? true
            : row["metadata/refund_credits"] === "false"
              ? false
              : null,
        stream:
          row["metadata/stream"] === "true"
            ? true
            : row["metadata/stream"] === "false"
              ? false
              : null,
        error_type: row["metadata/error_type"] || null,
        error_message: row["metadata/error_message"] || null,
        created_at: row.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    });

    // 生成CSV内容
    const headers = [
      "uuid",
      "user_uuid",
      "tags",
      "prompt",
      "gpt_description_prompt",
      "audio_prompt_id",
      "history",
      "concat_history",
      "type",
      "duration",
      "refund_credits",
      "stream",
      "error_type",
      "error_message",
      "created_at",
      "updated_at",
    ];

    const csvContent = [
      headers.join(","),
      ...transformedData.map((row) =>
        headers.map((header) => escapeCsvValue(row[header])).join(",")
      ),
    ].join("\n");

    // 保存CSV文件
    fs.writeFileSync(path.join(__dirname, "songs_import.csv"), csvContent);
    console.log(
      `Generated songs_import.csv with ${transformedData.length} records`
    );

    // 显示前几条数据用于验证
    console.log("\nFirst 3 records preview:");
    transformedData.slice(0, 3).forEach((record, index) => {
      console.log(`Record ${index + 1}:`, {
        uuid: record.uuid,
        user_uuid: record.user_uuid,
        tags: record.tags?.substring(0, 50) + "...",
        type: record.type,
        duration: record.duration,
      });
    });
  } catch (error) {
    console.error("Error processing data:", error);
  }
};

// 检查是否安装了csv-parser
try {
  require("csv-parser");
  processData();
} catch (e) {
  console.log("csv-parser not found. Installing...");
  console.log("Please run: npm install csv-parser");
  console.log("Then run this script again.");
}
