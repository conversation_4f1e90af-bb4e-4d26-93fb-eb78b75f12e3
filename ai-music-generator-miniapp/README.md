# AI Music Generator Mini Program

A powerful WeChat Mini Program for AI-powered music generation using Suno AI technology. Create, discover, and enjoy AI-generated music with an intuitive and modern interface.

## 📁 Project Structure

```
ai-music-generator-miniapp/
├── app.js                          # Main app entry point and global data
├── app.json                        # App configuration and page routing
├── app.wxss                        # Global styles
├── project.config.json             # WeChat Mini Program project config
├── plugin.json                     # WeUI plugin configuration
│
├── pages/                          # Application pages
│   ├── discover/                   # Music discovery and recommendations
│   ├── create/                     # AI music generation interface
│   ├── play/                       # Music playback interface
│   ├── user/                       # User profile and settings
│   ├── login/                      # Authentication pages
│   ├── account/                    # Account management
│   └── ...                         # Additional utility pages
│
├── components/                     # Reusable UI components
│   ├── dialog/                     # Modal dialogs
│   ├── musicList/                  # Music list displays
│   ├── navbar/                     # Navigation components
│   ├── tabs/                       # Tab controls
│   └── ...                         # Other UI components
│
├── services/                       # API service layer
│   └── index.js                    # Suno AI API integration
│
├── utils/                          # Utility functions
│   ├── constants.js                # App constants and configuration
│   ├── util.js                     # Helper functions
│   └── polyfill.js                 # Compatibility polyfills
│
├── custom-tab-bar/                 # Custom bottom navigation
├── donutAuthorize/                 # Authorization sub-package
├── image/                          # Static image assets
└── miniprogram_npm/                # NPM dependencies (WeUI)
```

## ✨ Core Features

### 🎼 AI Music Generation

- **Advanced AI Models**: Support for Suno AI V2 and V3 models
- **Lyrics Generation**: AI-powered lyric creation and customization
- **Style Customization**: Multiple music styles and genres
- **Music Continuation**: Extend existing tracks with AI
- **Real-time Generation**: Live progress tracking for music creation

### 🔍 Music Discovery

- **Curated Recommendations**: Personalized music discovery
- **Hot Songs Charts**: Trending AI-generated music
- **New Releases**: Latest creations from the community
- **Interactive Browsing**: Smooth navigation and preview

### 🎵 Music Playback

- **Background Audio**: Seamless music playback with background support
- **Queue Management**: Playlist and song queue functionality
- **Audio Controls**: Play, pause, skip, and seek controls
- **Visual Feedback**: Animated playback indicators

### 👤 User Management

- **WeChat Integration**: Native WeChat login and sharing
- **Phone Authentication**: Alternative login with verification codes
- **User Profiles**: Personal music libraries and preferences
- **Payment Integration**: WeChat Pay for premium features

### 💰 Monetization Features

- **Credit System**: Token-based generation limits
- **Package Plans**: Flexible pricing and subscription options
- **Payment Processing**: Secure WeChat Pay integration
- **Usage Tracking**: Real-time credit and usage monitoring

## 🛠 Key Technologies

### Frontend Framework

- **WeChat Mini Program**: Native mini program development
  - **WXML**: Markup language for UI structure
  - **WXSS**: Styling language for visual design
  - **JavaScript**: Application logic and API integration

### UI Components

- **WeUI**: Official WeChat UI component library
- **Custom Components**: Specialized music interface components
- **Responsive Design**: Optimized for various screen sizes

### Backend Integration

- **Suno AI API**: Advanced music generation services
- **RESTful APIs**: Structured API communication
- **Real-time Updates**: Live status tracking for generation tasks

### WeChat Ecosystem

- **Mini Program APIs**: Native WeChat functionality
- **Background Audio Manager**: Continuous music playback
- **Payment APIs**: Integrated WeChat Pay services
- **Sharing APIs**: Social sharing capabilities

### Development Tools

- **WeChat DevTools**: Official development environment
- **ES6+ Support**: Modern JavaScript features
- **NPM Integration**: Third-party package management
- **Hot Reload**: Development server with live updates

## 🚀 Quick Start

### Prerequisites

- **WeChat Developer Tools**: Download from [WeChat Official Site](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- **WeChat Developer Account**: Register at [WeChat MP Platform](https://mp.weixin.qq.com/)
- **Suno AI API Access**: Backend API service for music generation

### Installation Steps

1. **Clone the Repository**

   ```bash
   git clone <repository-url>
   cd ai-music-generator-miniapp
   ```

2. **Open in WeChat DevTools**

   - Launch WeChat Developer Tools
   - Import project and select the `ai-music-generator-miniapp` directory
   - Enter your Mini Program AppID when prompted

3. **Configure API Settings**

   ```javascript
   // utils/constants.js
   const CONFIG = {
     dev: "http://localhost:1651/api",
     test: "https://your-test-api.com/api",
     prod: "https://your-prod-api.com/api",
   };
   ```

4. **Update App Configuration**

   ```json
   // project.config.json
   {
     "appid": "your-miniprogram-appid",
     "compileType": "miniprogram"
   }
   ```

5. **Install Dependencies**

   - WeUI components are pre-configured in `plugin.json`
   - Dependencies will be automatically resolved by WeChat DevTools

6. **Development Setup**
   - Enable development mode in WeChat DevTools
   - Configure debugging options
   - Test on simulator or real device

### Backend Requirements

- Suno AI compatible backend service
- User authentication endpoints
- Payment processing integration
- Music file storage and delivery

### Deployment

1. **Upload Code**: Use WeChat DevTools to upload code
2. **Submit for Review**: Submit through WeChat MP Platform
3. **Configure Domains**: Add API domains to whitelist
4. **Release**: Publish after approval

## 📝 Configuration

### Environment Variables

```javascript
// utils/constants.js
const ENV = "prod"; // dev, test, prod
const VERSION = ENV + "1.5.2";
```

### API Endpoints

- Music Generation: `/suno/generate`
- Lyrics Generation: `/suno/generateLyrics`
- User Authentication: `/aigc/user/miniLogin`
- Payment Processing: `/aigc/pay/createJsApiPayInfo`

### Features Toggle

Configure available features through the backend API configuration system.

---

**Note**: This Mini Program requires a valid WeChat Developer Account and Suno AI API access for full functionality. Ensure all API endpoints are properly configured before deployment.
