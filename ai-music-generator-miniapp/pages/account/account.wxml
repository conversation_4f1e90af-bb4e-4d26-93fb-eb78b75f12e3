<view class="account">
    <view class="title">
        <!-- <text>管理订阅</text>
        <view class="sub_title">选择适合你的计划。</view> -->
    </view>
    <view class="item_list">
        <view class="item" wx:for="{{listPackage}}" wx:key="packageId">
            <view class="item_title">{{item.packageName}}</view>
            <view class="item_price">{{item.packagePrice}}</view>
            <view bind:tap="onSubmit" class="item_btn" data-item="{{item}}">订阅</view>
            <view class="item_hr"></view>
            <view class="item_text" wx:for="{{item.attributes}}" wx:for-item="attrItem" wx:key="index">
                <image class="item_icon" mode="widthFix" src="./../../image/icon11.svg"></image>
                <rich-text class="text" nodes="{{attrItem}}"></rich-text>
            </view>
        </view>
    </view>
</view>
