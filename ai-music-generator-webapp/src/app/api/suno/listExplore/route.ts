import { respData, respErr } from "@/lib/resp";
import { getMusics } from "@/models/music";
import { Music } from "@/types/music";
import logger from "@/lib/logger";

/**
 * Get music list of explore
 *
 * @param from Optional from
 * @param rankCategory rank category 
 * @returns
 */
export async function POST(req: Request) {
  try {
    const body = await req.json();
    // logger.info(`---listExplore req=`, body);
    const { from, rankCategory } = body;

    const pageSize = 50;

    // get musics by rankCategory
    const musics: Music[] = await getMusics(from, pageSize, rankCategory);

    // console.log(`---listExplore rsp=${JSON.stringify(musics)}`);

    return respData(musics);
  } catch (e) {
    console.error("listExplore error=", e);
    return respErr("listExplore");
  }
}
