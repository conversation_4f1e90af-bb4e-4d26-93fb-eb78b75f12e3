import { respData, respErr } from "@/lib/resp";
import { findUserByUserId } from "@/models/user";

export async function POST(req: Request) {
  try {
    // console.log(`---getUser req=${JSON.stringify(req.json)}`);
    //
    const api_id = req.headers.get("x-apiid") || "";
    const token = req.headers.get("x-token") || "";
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const user = await findUserByUserId(user_id);
    if(!user) {
      return respErr("user empty");
    }

    // console.log(`---getUser rsp=${JSON.stringify(user)}`);

    return respData(user);
  } catch (error) {
    console.error("getUser error=", error);
    return respErr("getUser error");
  }
}
