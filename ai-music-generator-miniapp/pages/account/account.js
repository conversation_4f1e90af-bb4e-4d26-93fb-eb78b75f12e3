var t = require('./../../services/index');
const { ENV, PayType, PayStatus, PeriodType } = require('../../utils/constants');

Page({
  data: { listPackage: [] },
  onLoad: function () {
    wx.hideShareMenu({});
  },
  onShow: function () {
    this.getListPackage(), wx.setNavigationBarTitle({ title: '会员充值' });
  },
  getListPackage: function () {
    var a = this;
    t.listAigcPackage({ environment: ENV }).then(function (t) {
      0 == t.code && a.setData({ listPackage: t.data });
    });
  },
  onSubmit: function (a) {
    var n = this;
    if (n.data.isProcessing) { // 检查是否正在处理中
      wx.showToast({
        type: 'error',
        icon: 'none',
        title: '正在处理中，请稍候...',
      });
      return;
    }
    // 设置正在处理中状态
    n.setData({
      isProcessing: true
    });
    wx.showLoading({
      title: '请稍候...',
      mask: true
    });
    var e = a.currentTarget.dataset.item;
    if (wx.getStorageSync('accessToken')) {
      t.createJsApiPayInfo({ apiPackageId: e.packageId }).then(function (t) {
        if (t.data) {
          wx.requestPayment({
            timeStamp: t.data.timeStamp,
            nonceStr: t.data.nonceStr,
            package: t.data.package,
            signType: t.data.signType,
            paySign: t.data.paySign,
            success: function (a) {
              n.onGetQrCodePayStatus(t.data.orderId);
            },
            fail: function (t) {
              console.log('requestPayment err=', t);
              wx.showToast({
                icon: 'none',
                title: '取消支付',
              });
            },
            complete: function (t) {
              console.log('compelte')
              // 无论成功失败，完成后都重置处理状态
              n.setData({
                isProcessing: false
              });
              wx.hideLoading(); // 隐藏加载提示
            },
          })
        } else {
          wx.showToast({
            icon: 'none',
            title: '获取支付信息失败，请联系客服。',
          });
          n.setData({
            isProcessing: false
          });
          wx.hideLoading(); // 隐藏加载提示
        }
      }).catch(function(error) {
        wx.hideLoading(); // 隐藏加载提示
        wx.showToast({
          icon: 'none',
          title: '请求支付信息出错',
        });
        n.setData({
          isProcessing: false
        });
      });
    } else {
      wx.showToast({
        icon: 'none',
        title: '请先登录，再进行充值。',
      });
      n.setData({
        isProcessing: false
      });
      wx.hideLoading(); // 隐藏加载提示
    }
  },
  onGetQrCodePayStatus: function (a) {
    console.log('---onGetQrCodePayStatus a=', a)
    t.getJsApiPayStatus({ orderId: a }).then(function (t) {
      var a;
      PayStatus.Success == (null === (a = t.data) || void 0 === a ? void 0 : a.payStatus) &&
        (wx.showToast({ title: '支付成功', type: 'success' }),
        wx.navigateBack());
    });
  },
});
