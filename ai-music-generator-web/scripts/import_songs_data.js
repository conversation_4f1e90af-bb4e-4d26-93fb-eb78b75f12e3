const fs = require('fs');
const path = require('path');

// 读取JSON数据
const jsonData = JSON.parse(fs.readFileSync(path.join(__dirname, '../src/db/init_data/suno.json'), 'utf8'));

// 转换数据格式
const transformedData = jsonData.map(item => {
  const metadata = item.metadata || {};
  
  return {
    uuid: item.id,
    user_uuid: item.user_id || 'default-user-uuid', // 如果没有user_id，使用默认值
    tags: metadata.tags || null,
    prompt: metadata.prompt || null,
    gpt_description_prompt: metadata.gpt_description_prompt || null,
    audio_prompt_id: metadata.audio_prompt_id || null,
    history: metadata.history ? JSON.stringify(metadata.history) : null,
    concat_history: metadata.concat_history ? JSON.stringify(metadata.concat_history) : null,
    type: metadata.type || null,
    duration: metadata.duration || null,
    refund_credits: metadata.refund_credits || null,
    stream: metadata.stream || null,
    error_type: metadata.error_type || null,
    error_message: metadata.error_message || null,
    created_at: item.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
});

// 生成SQL插入语句
const generateInsertSQL = (data) => {
  const values = data.map(item => {
    const escapedValues = [
      `'${item.uuid.replace(/'/g, "''")}'`,
      `'${item.user_uuid.replace(/'/g, "''")}'`,
      item.tags ? `'${item.tags.replace(/'/g, "''")}'` : 'NULL',
      item.prompt ? `'${item.prompt.replace(/'/g, "''")}'` : 'NULL',
      item.gpt_description_prompt ? `'${item.gpt_description_prompt.replace(/'/g, "''")}'` : 'NULL',
      item.audio_prompt_id ? `'${item.audio_prompt_id.replace(/'/g, "''")}'` : 'NULL',
      item.history ? `'${item.history.replace(/'/g, "''")}'` : 'NULL',
      item.concat_history ? `'${item.concat_history.replace(/'/g, "''")}'::jsonb` : 'NULL',
      item.type ? `'${item.type.replace(/'/g, "''")}'` : 'NULL',
      item.duration ? item.duration : 'NULL',
      item.refund_credits !== null ? item.refund_credits : 'NULL',
      item.stream !== null ? item.stream : 'NULL',
      item.error_type ? `'${item.error_type.replace(/'/g, "''")}'` : 'NULL',
      item.error_message ? `'${item.error_message.replace(/'/g, "''")}'` : 'NULL',
      `'${item.created_at}'`,
      `'${item.updated_at}'`
    ];
    
    return `(${escapedValues.join(', ')})`;
  });

  return `INSERT INTO songs (uuid, user_uuid, tags, prompt, gpt_description_prompt, audio_prompt_id, history, concat_history, type, duration, refund_credits, stream, error_type, error_message, created_at, updated_at) VALUES\n${values.join(',\n')};`;
};

// 分批处理数据（每批1000条）
const batchSize = 1000;
const batches = [];

for (let i = 0; i < transformedData.length; i += batchSize) {
  const batch = transformedData.slice(i, i + batchSize);
  batches.push(batch);
}

// 生成SQL文件
batches.forEach((batch, index) => {
  const sql = generateInsertSQL(batch);
  const filename = `insert_songs_batch_${index + 1}.sql`;
  fs.writeFileSync(path.join(__dirname, filename), sql);
  console.log(`Generated ${filename} with ${batch.length} records`);
});

console.log(`Total batches generated: ${batches.length}`);
console.log(`Total records: ${transformedData.length}`);
