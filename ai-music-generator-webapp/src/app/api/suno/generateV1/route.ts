import { genUuid } from "@/lib/index";
import { AudioInfo, sunoApi } from "@/lib/SunoApi";
import { respData, respErr } from "@/lib/resp";
import { insertMusic } from "@/models/music";
import { getUserCredits } from "@/services/order";
import { saveTask } from "@/services/task";
import { Music } from "@/types/music";
import { Task } from "@/types/task";

export async function POST(req: Request) {
  try {
    const startTime = Date.now();
    const user_id = req.headers.get("x-user-id") || "";
    if (!user_id) {
      return respErr("user_id is required");
    }

    const body = await req.json();
    console.log(`---generate req=${body}`);

    const {
      inputType, // 类型 10=灵感 20=自定义
      mvVersion,
      makeInstrumental,
      action,
      idea,
      lyric,
      style,
      title,
      wait_audio,
    } = body;

    const startTime_getUserCredits = Date.now();

    // check user remainCount
    const user_credits = await getUserCredits(user_id);
    if (!user_credits || user_credits.leftCoin < 1) {
      return respErr("剩余的积分已不够，充值可获得更多积分");
    }

    const costTime_getUserCredits = Date.now() - startTime_getUserCredits;

    const startTime_generate = Date.now();

    // ktodo 优化效率
    let audioInfos: AudioInfo[] = [];
    if (inputType === 10) {
      if (!idea) {
        return respErr("idea is required");
      }
      audioInfos = await (
        await sunoApi
      ).generate(idea, makeInstrumental == true, wait_audio == true);
    } else if (inputType === 20) {
      if (!lyric) {
        return respErr("lyric is required");
      }
      audioInfos = await (
        await sunoApi
      ).custom_generate(
        lyric,
        style,
        title,
        makeInstrumental == true,
        wait_audio == true
      );
    } else {
      return respErr("Invaild inputType");
    }
    console.log(`---generate audioInfos=${JSON.stringify(audioInfos)}`);

    const costTime_generate = Date.now() - startTime_generate;

    const startTime_insertMusic = Date.now();

    // insert into musics
    let music_ids: string[] = []; // Ensure music_ids is initialized
    audioInfos.forEach(async (audioInfo) => {
      music_ids.push(audioInfo.id);
      const music: Music = {
        music_id: audioInfo.id,
        user_id: user_id,
        video_url: audioInfo.video_url,
        audio_url: audioInfo.audio_url,
        image_url: audioInfo.image_url,
        model_name: audioInfo.model_name,
        status: audioInfo.status,
        title: audioInfo.title,
        created_at: audioInfo.created_at,
        music_metadata: {
          music_id: audioInfo.id,
          user_id: user_id,
          tags: audioInfo.tags,
          prompt: audioInfo.prompt,
          gpt_description_prompt: audioInfo.gpt_description_prompt,
          type: audioInfo.type,
          duration: Number(audioInfo.duration),
          created_at: audioInfo.created_at,
        },
      };
      console.log(`---insert music=${JSON.stringify(music)}`);
      await insertMusic(music);
    });
    console.log(`---generate rsp=${JSON.stringify(audioInfos)}`);

    const costTime_insertMusic = Date.now() - startTime_insertMusic;

    const startTime_saveTask = Date.now();

    // insert into tasks
    const task_id = genUuid();
    const task: Task = {
      task_id: task_id,
      music_ids: music_ids,
      user_id: user_id,
      clips: JSON.stringify(audioInfos),
      batch_size: 2,
      // default should be "queued", update by batchQuery
      status: "queued",
    };
    await saveTask(task);

    const costTime_saveTask = Date.now() - startTime_saveTask;

    const costTime = Date.now() - startTime;

    console.log(
      `---costTime_getUserCredits=${costTime_getUserCredits}, costTime_generate=${costTime_generate}, costTime_insertMusic=${costTime_insertMusic}, costTime_saveTask=${costTime_saveTask}, costTime=${costTime}`
      //
    );

    return respData({
      id: task_id,
    });
  } catch (error) {
    console.error("generate error=", error);
    return respErr("generate error");
  }
}
