<view animation="{{animationData}}" class="tab-bar" wx:if="{{isShowTabBar}}">
    <view bindtap="switchTab" class="tab-bar-item {{item.bulge?'bulge':''}}" data-index="{{index}}" data-path="{{item.pagePath}}" wx:for="{{list}}" wx:key="index">
        <view class="tab-bar-bulge {{active?'active':''}}" wx:if="item.bulge"></view>
        <view class="{{active?'active':''}}" wx:if="{{item.bulge}}">
            <image class="oprimg {{active?'active':''}}" src="{{item.iconPath}}"></image>
            <view class="text" style="color: {{selected===index?selectedColor:color}}">{{item.text}}</view>
        </view>
        <view wx:else>
            <image class="img" src="{{selected===index?item.selectedIconPath:item.iconPath}}"></image>
            <view class="text" style="color: {{selected===index?selectedColor:color}}">{{item.text}}</view>
        </view>
    </view>
</view>
