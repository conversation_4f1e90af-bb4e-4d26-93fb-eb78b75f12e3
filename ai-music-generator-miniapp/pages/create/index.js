var t = require('../../@babel/runtime/helpers/slicedToArray'),
  a = require('../../@babel/runtime/helpers/defineProperty'),
  e = require('../../services/index'),
  i = require('../../utils/util.js');
Page({
  data: {
    errorMsg: '',
    switchVal: '10',
    switchVal2: !1,
    tipsFlag: !1,
    options: [
      { value: 'chirp-v3-0', name: 'V3' },
      { value: 'chirp-v2-xxl-alpha', name: 'V2' },
    ],
    params: { lyric3: '', lyric2: '', idea: '' },
    selectedValue: 0,
    displayOptions: [],
    action: 'generate',
    continueSong: '',
    continueAtValue: '',
    isGenerateLyricsLoading: !1,
    disabled: !0,
    clipFlag: !0,
    tabIndex: 1,
    // disabled: true // 初始禁用状态
  },
  onLoad: function () {
    wx.showShareMenu({
      withShareTicket: !0,
      menus: ['shareAppMessage', 'shareTimeline'],
    }),
      this.setData({
        displayOptions: this.data.options.map(function (t) {
          return t.name;
        }),
      });
  },
  onShow: function () {
    wx.pageScrollTo({ scrollTop: 0 }),
      'function' == typeof this.getTabBar &&
        this.getTabBar() &&
        this.getTabBar().setData({ selected: 1 });
    var t = wx.getStorageSync('songDetail'),
      e = wx.getStorageSync('createAction');
    if (t) {
      var s;
      if ((this.setData({ switchVal: '20' }), 'generate' == e))
        this.setData(
          (a((s = {}), 'params.style', t.style),
          a(s, 'params.title', t.title),
          a(s, 'params.lyric', t.lyric),
          a(s, 'disabled', !1),
          s),
        );
      else if ('continue' == e) {
        var n = i.formatSecondsToMMSS(t.music_metadata.duration);
        // debugger;
        this.setData({
          action: 'continue',
          continueAtValue: n,
          continueSong: t,
        });
      }
      'v3' == t.mvVersion
        ? this.setData({ selectedValue: 0 })
        : 'v2' == t.mvVersion && this.setData({ selectedValue: 1 }),
        wx.removeStorage({ key: 'createAction' }),
        wx.removeStorage({ key: 'songDetail' });
    }
  },
  toggle: function (t) {
    var e,
      i = t.currentTarget.dataset.val;
    this.setData(
      (a(
        (e = { switchVal: i, switchVal2: !1, switchVal3: !1 }),
        'params.idea',
        '',
      ),
      a(e, 'params.style', ''),
      a(e, 'params.title', ''),
      a(e, 'params.lyric', ''),
      a(e, 'action', 'generate'),
      a(e, 'continueSong', null),
      a(e, 'params.continueAt', ''),
      a(e, 'disabled', !0),
      e),
    );
  },
  toggle2: function () {
    this.setData({ switchVal2: !this.data.switchVal2 });
  },
  toggle3: function () {
    var t = this;
    this.setData({ switchVal3: !this.data.switchVal3 }, function () {
      t.data.switchVal3 && t.setData(a({}, 'params.lyric', ''));
    });
  },
  showTips: function () {
    this.setData({ tipsFlag: !this.data.tipsFlag });
  },
  hideTip: function () {
    this.setData({ tipsFlag: !1 });
  },
  onDisable: function (t) {
    t ? this.setData({ disabled: !1 }) : this.setData({ disabled: !0 });
  },
  bindPickerChange: function (t) {
    this.setData({ selectedValue: t.detail.value });
  },
  bindTextInput: function (t) {
    this.setData(a({}, 'params.idea', t.detail.value));
    this.onDisable(t.detail.value.trim()); // 确保传入的是已处理的字符串
  },
  // bindTextInput: function (t) {
  //   this.setData({
  //     'params.idea': t.detail.value
  //   });
  //   this.onDisable(t.detail.value.trim()); // 确保传入的是已处理的字符串
  // },
  tabChange: function (t) {
    var e,
      i = t.currentTarget.dataset.index;
    this.setData(
      (a((e = { tabIndex: i }), 'params.lyric', ''),
      a(e, 'params.lyric2', ''),
      e),
    );
  },
  bindLyricInput: function (t) {
    this.setData(a({}, 'params.lyric', t.detail.value)),
      t.detail.value ? this.onDisable(1) : this.onDisable(0);
  },
  bindLyric2Input: function (t) {
    this.setData(a({}, 'params.lyric2', t.detail.value));
  },
  onGenerateLyrics: function () {
    var t = this;
    if (!this.data.params.lyric2)
      return this.setData({ errorMsg: '请输入歌词参考信息。' }), !1;
    this.setData({ isGenerateLyricsLoading: !0 }),
      e
        .generateLyrics({ idea: this.data.params.lyric2 })
        .then(function (e) {
          var i;
          0 === e.code &&
            (t.setData(
              (a((i = {}), 'params.lyric2', e.data.text),
              a(i, 'params.lyric3', e.data.text),
              a(i, 'params.title', e.data.title),
              i),
            ),
            t.onDisable(1));
        })
        .finally(function () {
          t.setData({ isGenerateLyricsLoading: !1 });
        });
  },
  bindStyleInput: function (t) {
    this.setData(a({}, 'params.style', t.detail.value)),
      this.onDisable(t.detail.value);
  },
  onRandomStyle: function (t) {
    this.setData(a({}, 'params.style', i.randomStyle())), this.onDisable(1);
  },
  bindTitleInput: function (t) {
    this.setData(a({}, 'params.title', t.detail.value));
  },
  bindContinueAtInput: function (e) {
    var s = e.detail.value;
    if (i.isValidMMSSFormat(s)) {
      var n,
        r = s.split(/[:：]/),
        l = t(r, 2),
        o = l[0],
        c = l[1],
        u = 60 * parseInt(o, 10) + parseInt(c, 10);
      if (u <= Number(this.data.continueSong.clipDuration).toFixed())
        this.setData(
          (a((n = {}), 'params.continueAt', u.toString()),
          a(n, 'clipFlag', !0),
          n),
        );
      else
        this.setData({
          errorMsg: '剪辑时长不能超过当前这首歌的时长',
          clipFlag: !1,
        });
    } else
      this.setData({
        errorMsg: '请输入mm:ss格式（例如 00:30）。',
        clipFlag: !1,
      });
  },
  onClear: function () {
    this.setData(
      a(
        { action: 'generate', continueSong: null, clipFlag: !0 },
        'params.continueAt',
        '',
      ),
    );
  },
  onSubmit: function () {
    var t = this;
    // 阻止重复提交
    if (t.data.isSubmitting) return;
    t.setData({ isSubmitting: true });
  
    if (!wx.getStorageSync('accessToken')) {
      wx.navigateTo({ url: '/pages/login/login' });
      t.setData({ isSubmitting: false });
      return false;
    }
    
    if (!this.data.clipFlag) {
      this.setData({ errorMsg: '请输入正确的剪辑时长。', isSubmitting: false });
      return false;
    }
    
    var a = this.data,
      i = a.switchVal,
      s = a.switchVal2,
      n = a.switchVal3,
      r = a.selectedValue,
      l = a.action,
      o = a.options,
      c = a.continueSong,
      u = a.params,
      d = a.tabIndex;
    
      console.log('a=', a)

    if ('10' == i && !u.idea) {
      wx.showToast({ title: '请输入歌曲描述。', icon: 'none' });
      this.setData({ errorMsg: '请输入歌曲描述。', isSubmitting: false });
      return false;
    }
    
    if ('20' == i && '' == u.lyric2 && '' == u.lyric && '' == u.style) {
      wx.showToast({ title: '请输入音乐风格或者歌词。', icon: 'none' });
      this.setData({ errorMsg: '请输入音乐风格或者歌词。', isSubmitting: false });
      return false;
    }
    
    var h = {
      inputType: Number(i),
      mvVersion: o[r].value,
      makeInstrumental: 20 == i ? (n ? 1 : 0) : s ? 1 : 0,
      action: l,
      // continueClipId: null == c ? undefined : c.clipId,
      continueClipId: null == c ? undefined : c.music_id,
      continueAt: u.continueAt || (null == c ? undefined : c.clipDuration),
      // continueAt: u.continueAt || (null == c ? undefined : c.music_metadata.duration),
      idea: u.idea,
      lyric: 1 == d ? u.lyric : u.lyric3,
      style: u.style,
      title: u.title,
    };
  
    wx.showLoading({ title: '排队中...' }); // 显示加载提示框
    
    e.generate(h).then(function (a) {
      wx.hideLoading(); // 隐藏加载提示框
      if (0 === a.code) {
        wx.setStorageSync('generateId', a.data.id);
        wx.switchTab({ url: '/pages/user/user' });
      } else {
        wx.showToast({ title: a.msg, icon: 'none' });
        t.setData({ errorMsg: a.msg });
      }
      t.setData({ isSubmitting: false }); // 重置提交状态
    }).catch(function (error) {
      wx.hideLoading(); // 出错也要确保隐藏加载提示框
      wx.showToast({ title: '请求失败', icon: 'none' });
      t.setData({ isSubmitting: false }); // 重置提交状态
    });
  },
});
