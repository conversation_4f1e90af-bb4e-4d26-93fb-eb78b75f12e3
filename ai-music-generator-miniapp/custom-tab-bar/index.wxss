.tab-bar {
  background: #1f2022;
  bottom: 0;
  height: 48px;
  left: 0;
  padding: 10rpx 0;
  pointer-events: auto;
  position: absolute;
  position: fixed;
  right: 0;
}
.tab-bar,
.tab-bar-item {
  display: -webkit-flex;
  display: flex;
}
.tab-bar-item {
  align-items: center;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}
.tab-bar-item .img {
  height: 40rpx;
  width: 40rpx;
}
.tab-bar-item .text {
  font-size: 24rpx;
}
.bulge .tab-bar-bulge {
  background-color: #272626;
  border-radius: 50%;
  height: 102rpx;
  position: absolute;
  top: -60rpx;
  transition: all 0.3s;
  width: 102rpx;
  z-index: 1;
}
.bulge .tab-bar-bulge.active {
  height: 80rpx;
  top: -50rpx;
  width: 80rpx;
}
.bulge .oprimg {
  height: 50rpx;
  left: 50%;
  margin-left: -26rpx;
  position: absolute;
  top: -35rpx;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  width: 50rpx;
  z-index: 9;
}
.bulge .oprimg.active {
  transform: rotateZ(45deg);
  transform-origin: center center;
}
.bulge .text {
  bottom: -18rpx;
  margin-top: 4px;
  position: relative;
}
.tab-bar-item .tab-bar-view {
  font-size: 12px;
  margin-top: 4px;
}
.img2text {
  background: #000;
  border: 4rpx solid #b0b2b5;
  border-radius: 50%;
  color: #fff;
  font-size: 38rpx;
  height: 100rpx;
  margin-left: -7px;
  opacity: 0;
  position: absolute;
  top: -58rpx;
  transition: all 0.3s;
  width: 100rpx;
}
.img2text .img {
  height: 98rpx;
  width: 98rpx;
}
.bulge .active .img2text {
  margin-left: -125rpx;
  opacity: 1;
  top: -120rpx;
}
.img2img {
  background: #000;
  border: 4rpx solid #4075ff;
  border-radius: 50%;
  color: #fff;
  font-size: 38rpx;
  height: 100rpx;
  margin-left: -7px;
  opacity: 0;
  position: absolute;
  top: -58rpx;
  transition: all 0.3s;
  width: 100rpx;
}
.img2img .img {
  height: 98rpx;
  width: 98rpx;
}
.bulge .active {
  transition: all 0.3s;
}
.bulge .active .img2img {
  margin-left: -13rpx;
  opacity: 1;
  top: -200rpx;
}
.text2img .img {
  height: 98rpx;
  width: 98rpx;
}
.text2img {
  background: #000;
  border: 4rpx solid #b0b2b5;
  border-radius: 50%;
  color: #fff;
  height: 100rpx;
  margin-left: -7px;
  opacity: 0;
  position: absolute;
  top: -58rpx;
  transition: all 0.3s;
  width: 100rpx;
}
.img2img .text,
.img2text .text,
.text2img .text {
  line-height: 35px;
}
.bulge .active .text2img {
  margin-left: 100rpx;
  opacity: 1;
  top: -120rpx;
}
@-webkit-keyframes rotating {
  0% {
    transform: rotateZ(0);
  }
  100% {
    transform: rotateZ(45deg);
  }
}
@keyframes rotating {
  0% {
    transform: rotateZ(0);
  }
  100% {
    transform: rotateZ(45deg);
  }
}
