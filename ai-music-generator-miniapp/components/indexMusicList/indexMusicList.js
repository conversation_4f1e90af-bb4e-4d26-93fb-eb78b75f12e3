var t = getApp().globalData,
  e = (require('../../services/index'), wx.getBackgroundAudioManager()),
  i = null;
Component({
  properties: {
    musicList: { type: Array },
    extClass: { type: String, value: '' },
  },
  data: { list: [], songDetail: {}, isPlay: !1, playTime: '' },
  observers: {
    musicList: function (t) {
      console.log(t), this.setData({ list: t });
    },
  },
  lifetimes: { attached: function () {} },
  pageLifetimes: { show: function () {} },
  methods: {
    createAudio: function (t) {
      var a = this;
      (e.src = t.audioUrl), (e.title = t.title), (e.coverImgUrl = t.image_url);
      var s = '';
      e.onPlay(function () {
        console.log('开始播放');
        var t = e.duration,
          n = e.currentTime;
        i = setInterval(function () {
          (n = e.currentTime),
            (s = 100 * Number(n / t)),
            console.log(n, t, s),
            a.setData({ playTime: s });
        }, 500);
      }),
        e.onError(function (t) {
          console.log(t.errMsg), console.log(t.errCode);
        }),
        e.onPause(function () {
          console.log('暂停播放'), clearInterval(i);
        });
    },
    audioPlay: function (t) {
      var e = t.currentTarget.dataset.item;
      this.createAudio(e), this.setData({ songDetail: e, isPlay: !0 });
    },
    audioPause: function () {
      e.pause(), clearInterval(i), this.setData({ isPlay: !1 });
    },
    selectItem: function (e) {
      (t.currentIndex = e.currentTarget.dataset.index),
        (t.songlist = this.data.list),
        wx.setStorageSync('songlist', this.data.list),
        wx.navigateTo({ url: '/pages/play/play' });
    },
    onLike: function (t) {
      var e = t.currentTarget.dataset.index,
        i = this.data.list;
      i[e].audioUrl &&
        ((i[e].isLike = !i[e].isLike),
        i[e].isLike
          ? (i[e].likes = i[e].likes + 1)
          : (i[e].likes = i[e].likes - 1)),
        this.setData({ list: i });
    },
    onShare: function (t) {},
    openMore: function (t) {
      this.triggerEvent('myCustomEvent', t.currentTarget.dataset.item, {});
    },
  },
});
