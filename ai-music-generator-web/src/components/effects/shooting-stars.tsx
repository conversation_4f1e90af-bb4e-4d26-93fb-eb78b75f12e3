"use client";

import { useEffect, useMemo, useState } from "react";

type ShootingStarsProps = {
  /** Number of stars rendered concurrently */
  count?: number;
};

/**
 * ShootingStars renders a lightweight, self-contained background of animated meteors.
 * - No external CSS files; styles are scoped via styled-jsx.
 * - Uses CSS variables per-star to keep the CSS simple and easy to tune.
 */
export default function ShootingStars({ count = 10 }: ShootingStarsProps) {
  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);

  const stars = useMemo(() => {
    const items: Array<{
      id: number;
      delaySec: number;
      durationSec: number;
      topOffsetPx: number;
      leftOffsetPx: number;
      tailPx: number;
      travelPx: number;
      opacity: number;
    }> = [];

    for (let i = 0; i < count; i += 1) {
      const delaySec = Math.random() * 10; // 0s – 10s
      const durationSec = 2.8 + Math.random() * 1.6; // 2.8s – 4.4s
      const topOffsetPx = Math.floor(Math.random() * 400 - 200); // -200px – 200px
      const leftOffsetPx = Math.floor(Math.random() * 300); // 0px – 300px
      const tailPx = 80 + Math.random() * 60; // 80px – 140px
      const travelPx = 260 + Math.random() * 120; // 260px – 380px
      const opacity = 0.55 + Math.random() * 0.4; // 0.55 – 0.95

      items.push({
        id: i,
        delaySec,
        durationSec,
        topOffsetPx,
        leftOffsetPx,
        tailPx,
        travelPx,
        opacity,
      });
    }
    return items;
  }, [count]);

  if (!mounted) return null;

  return (
    <div className="night" aria-hidden>
      {stars.map((s) => (
        <div
          key={s.id}
          className="shooting-star"
          style={{
            top: `calc(50% - ${s.topOffsetPx}px)`,
            left: `calc(50% - ${s.leftOffsetPx}px)`,
            opacity: s.opacity,
            // CSS variables to keep keyframes generic
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore: CSS custom properties
            "--d": `${s.durationSec}s`,
            // @ts-ignore: CSS custom properties
            "--delay": `${s.delaySec}s`,
            // @ts-ignore: CSS custom properties
            "--tail": `${s.tailPx}px`,
            // @ts-ignore: CSS custom properties
            "--travel": `${s.travelPx}px`,
          }}
        />
      ))}

      <style jsx>{`
        .night {
          position: absolute;
          inset: 0;
          overflow: hidden;
          pointer-events: none;
          z-index: 0; /* background layer within the section */
          transform: rotate(45deg);
        }

        .shooting-star {
          position: absolute;
          height: 2px;
          border-radius: 999px;
          background: linear-gradient(
            -45deg,
            rgba(95, 145, 255, 1),
            rgba(0, 0, 255, 0)
          );
          filter: drop-shadow(0 0 6px rgba(105, 155, 255, 1));
          animation-name: tail, shooting;
          animation-timing-function: ease-in-out, ease-in-out;
          animation-iteration-count: infinite, infinite;
          animation-duration: var(--d), var(--d);
          animation-delay: var(--delay), var(--delay);
        }

        .shooting-star::before,
        .shooting-star::after {
          content: "";
          position: absolute;
          top: calc(50% - 1px);
          right: 0;
          height: 2px;
          border-radius: 100%;
          background: linear-gradient(
            -45deg,
            rgba(0, 0, 255, 0),
            rgba(95, 145, 255, 1),
            rgba(0, 0, 255, 0)
          );
          transform: translateX(50%) rotate(45deg);
          animation: shining var(--d) ease-in-out infinite;
          animation-delay: var(--delay);
        }

        .shooting-star::after {
          transform: translateX(50%) rotate(-45deg);
        }

        @keyframes tail {
          0% {
            width: 0;
          }
          30% {
            width: var(--tail);
          }
          100% {
            width: 0;
          }
        }

        @keyframes shining {
          0% {
            width: 0;
          }
          50% {
            width: 30px;
          }
          100% {
            width: 0;
          }
        }

        @keyframes shooting {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(var(--travel));
          }
        }
      `}</style>
    </div>
  );
}
